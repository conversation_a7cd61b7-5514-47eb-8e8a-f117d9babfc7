<template>
  <BreadCrumb :list="[
    {
      url: '/warranty',
      name: 'Warrant<PERSON>',
    },
  ]" />

  <div class="container-view">
    <div class="sidebar">
      <div class="button-group">
        <el-button type="primary" @click="getList" block style="margin: 0">Search the registration</el-button>
        <el-button type="danger" block @click="registerNewProduct()" style="margin: 0">Register new product</el-button>
      </div>

      <div class="side-form">
        <el-form :label-position="'top'">
          <el-form-item label="Name:">
            <el-input placeholder="Customer Name" v-model="searchForm.linkman"></el-input>
          </el-form-item>
          <el-form-item label="Phone,Email:">
            <el-input placeholder="Phone, Email" v-model="searchForm.phone"></el-input>
          </el-form-item>
          <el-form-item label="Start date:">
            <el-date-picker value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder="Start date" style="width: 100%"
              v-model="searchForm.startDate"></el-date-picker>
          </el-form-item>
          <el-form-item label="End date:">
            <el-date-picker value-format="YYYY-MM-DD" format="YYYY-MM-DD" v-model="searchForm.endDate"
              placeholder="End date" style="width: 100%"></el-date-picker>
          </el-form-item>
          <el-radio-group v-model="searchForm.status" style="
              display: flex;
              flex-direction: column;
              align-items: flex-start;
            ">
            <el-radio label="">All</el-radio>
            <div class="radio-item">
              <el-radio label="1" style="margin: 0">To be confirmed</el-radio>
              <div class="color-block red"></div>
            </div>
            <div class="radio-item">
              <el-radio label="2" style="margin: 0">Confirmed</el-radio>
              <div class="color-block blue"></div>
            </div>
          </el-radio-group>
          <!--          <div class="radio-item">
            <el-checkbox v-model="searchForm.hideInvalid"
              >Do not display invalid</el-checkbox
            >
            <div class="color-block grey"></div>
          </div>-->
        </el-form>
      </div>
    </div>
    <div class="content">
      <el-table class="home-table" :data="itemsTable" style="width: 100%" :header-row-style="{
        height: '32px',
      }" :header-cell-style="{
        background: '#f5f5f5',
        fontWeight: 'normal',
        color: '#213547',
        fontSize: '12px',
      }">

        <el-table-column label="Date" width="190px">
          <template #default="{ row }">
            <div style="padding-left: 20px;">
              <!-- 顶部颜色 -->
              <div :style="{
                position: 'absolute',
                minWidth: '10px',
                width: '10px',
                background: row.status == 2 ? '#409eff' : '#f56c6c',
                top: 0,
                left: 0,
                height: '100%',
              }"></div>
              <el-form-item label="sales:" label-width="60px" label-position="left">
                {{ row.salesDate }}
              </el-form-item>
              <el-form-item label="creation:" label-width="60px" label-position="left">
                {{ row.updateDate }}
              </el-form-item>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="Customer Information" header-align="center">
          <template #default="{ row }">
            <div>
              <div class="infor-item">
                <div :title="row.name">{{ row.linkman }}</div>
                <div :title="row.city">{{ row.city }}</div>
              </div>

              <div class="infor-item">
                <div :title="row.email">{{ row.email }}</div>
                <div :title="row.address">{{ row.address }}</div>
              </div>

              <div class="infor-item">
                <div :title="row.phone">{{ row.phone }}</div>
                <div :title="row.companyName">{{ row.companyName }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="Equipment" width="230px">
          <template #default="{ row }">
            <div class="equipment-item">{{ row.productType }}</div>
            <div class="equipment-item">{{ row.bom }}</div>
            <div class="equipment-item">{{ row.serialNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column label="Operate:Registration" prop="operate" width="200px">
          <template #default="{ row }">
            <div style="display: flex;   align-items: center; justify-content: center; ">
              <div class="operate-view" style="display: flex;flex-direction: column;">
                <el-link type="primary" @click="getDetail(row)">Details</el-link>
                <el-link @click="confirm(row)" v-if="row.status === '1'"
                  :type="row.status === '1' ? 'danger' : 'primary'">Confirm</el-link>
              </div>
              <div style="display: flex;flex-direction: column;margin-left: 12px;">
                <el-link @click="invalid(row)" v-if="row.status === '1'"
                  :type="row.status === '1' ? 'danger' : 'primary'">Invalid</el-link>
              </div>

            </div>


          </template>
        </el-table-column>
        <el-table-column label="Warranty claims records" align="center" width="200px">
          <template #default="{ row }">
            <div style="
                display: flex;
                align-items: center;
                justify-content: center;
              ">
              <el-button class="add-button" @click="addWarrantyRecord(row)">
                <el-icon>
                  <Plus />
                </el-icon>
              </el-button>
              <el-scrollbar style="flex: 1;" :always="true" max-height="76px" v-if="row?.warrantyDates?.length > 0">
                <div v-for="date in row.warrantyDates" style="text-align: left">
                  <el-link type="primary" @click="detailWarrantyRecord(row, date)" style="text-decoration: underline">
                    {{ date.updateDate }}
                  </el-link>
                  <el-link v-if="isWithin24Hours(date.createDate)" style="margin-left: 5px;text-decoration: underline"
                    @click="invalidItem(date)" type="danger">Invalid</el-link>
                </div>
              </el-scrollbar>
            </div>
          </template>
        </el-table-column>

      </el-table>
      <div style="display: flex; justify-content: flex-end; margin-top: 8px">
        <el-pagination layout="prev, pager, next" :total="params.total" :page-size="params.pageSize"
          @current-change="currentPageChange($event)" :current-page="params.pageNo" />
      </div>
    </div>
  </div>
  <!-- 登记弹窗 -->
  <el-dialog v-model="showRegisterDialog" title="Product registration" style="margin-top: 50px;" width="90%"
    :before-close="registerDialogBeforClose" :show-close="false">

    <!-- 自定义按钮区域 -->
    <div class="custom-top-confirm">
      <el-button v-if="!registerFormDisabled" type="primary" @click="submitRegister">
        Confirm
      </el-button>
      <el-button @click="showRegisterDialog = false">Cancel</el-button>
    </div>
    <div style="height: 10px"></div>
    <el-form label-width="200px" :label-position="'top'" :model="registerForm" size="large" ref="registerFormRef"
      :disabled="registerFormDisabled" class="custom-disabled-form">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="Equipment/Engine" prop="brandId"
            :rules="{ required: true, message: 'Equipment/Engine is required', trigger: 'blur' }">
            <el-select placeholder="Equipment/Engine" v-model="registerForm.brandId" filterable clearable
              @change="selectChange('brand', $event)">
              <el-option v-for="item in getSelectList('brand')" :key="item.id" :label="item.fileName"
                :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="Equipment Type" prop="eqId"
            :rules="{ required: true, message: 'Equipment Type is required', trigger: 'blur' }">
            <el-select placeholder="Equipment Type" v-model="registerForm.eqId" filterable clearable
              @change="selectChange('eq', $event)">
              <el-option v-for="item in getSelectList('eq', registerForm.brandId)" :key="item.id" :label="item.fileName"
                :value="item.id" clearable />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="Equipment Model" prop="modelId"
            :rules="{ required: true, message: 'Equipment Model is required', trigger: 'blur' }">
            <el-select placeholder="Equipment Model" v-model="registerForm.modelId"
              @change="selectChange('model', $event)" filterable clearable>
              <el-option v-for="item in getSelectList('model', registerForm.eqId)" :key="item.id" :label="item.fileName"
                :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="Service Code" prop="versionId"
            :rules="{ required: true, message: 'Service Code is required', trigger: 'blur' }">
            <el-select placeholder="Service Code" v-model="registerForm.versionId"
              @change="selectChange('version', $event)" filterable clearable>
              <el-option v-for="item in getSelectList('version', registerForm.modelId)" :key="item.id"
                :label="item.fileName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="Sales date" prop="salesDate"
            :rules="{ required: true, message: 'Sales Date is required', trigger: 'blur' }">
            <el-date-picker value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder="Select date" style="width: 100%"
              v-model="registerForm.salesDate"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="Serial number" prop="serialNumber"
            :rules="{ required: true, message: 'Serial Number is required', trigger: 'blur' }">
            <el-input placeholder="Enter serial number" v-model="registerForm.serialNumber"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="Note" prop="note">
            <el-input type="textarea" v-model="registerForm.description" :rows="2" placeholder="Enter note"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-col :span="24">
        <el-form-item label="Sales Receipt" prop="pictureList_receipt">
          <el-upload v-model:file-list="registerForm.pictureList_receipt" list-type="picture-card"
            :on-preview="handlePictureCardPreview" :on-remove="handleRemove" :before-upload="beforeAvatarUpload"
            accept=".png,.jpg" :auto-upload="false">
            <el-icon>
              <Plus />
            </el-icon>
          </el-upload>
        </el-form-item>
      </el-col>
    </el-form>

    <el-form :label-position="'top'" :model="selectedUserForm" :disabled="true" size="large" ref="selectedUserRef"
      class="custom-disabled-form">
      <div style="padding: 10px;font-size: 16px;">
        <div v-if="!registerFormDisabled" style="background-color: #cccccc; text-align: center">
          <a @click="dialogVisible_customerFunc()">
            <img style="vertical-align: middle; height: 20px" src="../../assets/images/main/add_wihte.svg" alt="" />
            &nbsp; Select Customer Or Create New Customer
          </a>
        </div>
        <el-row :gutter="20" style="margin-top: 10px">
          <el-col :span="12">
            <el-form-item label="Name" prop="name">
              <el-input placeholder="Name" v-model="selectedUserForm.linkman"></el-input>
            </el-form-item>
          </el-col>
          <!--                <el-col :span="12">
                  <el-form-item label="Company name" prop="companyName">
                    <el-input
                      placeholder="Company name"
                      v-model="selectedUserForm.companyName"
                    ></el-input>
                  </el-form-item>
                </el-col>-->
          <el-col :span="12">
            <el-form-item label="Phone" prop="phone">
              <el-input placeholder="Phone" v-model="selectedUserForm.phone"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="E-mail" prop="email">
              <el-input placeholder="E-mail" v-model="selectedUserForm.email"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="Business Street Address" prop="address">
              <el-input placeholder="Street Address" v-model="selectedUserForm.address"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="City" prop="city">
              <el-input placeholder="City" v-model="selectedUserForm.city"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="State" prop="state">
              <el-input placeholder="State" v-model="selectedUserForm.state"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Country" prop="country">
              <el-input placeholder="Country" v-model="selectedUserForm.country"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Zip/Postal code" prop="zipCode">
              <el-input placeholder="Zip/Postal code" v-model="selectedUserForm.zipCode"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </div>
    </el-form>


  </el-dialog>
  <!-- 维修索赔 -->
  <el-dialog title="Warranty claims " v-model="dialogVisible_warranty_claim" style="margin-top: 50px" width="90%"
    :show-close="false" :before-close="claimDialogBeforClose">

    <!-- 自定义按钮区域 -->
    <div class="custom-top-confirm">
      <el-button v-if="!claimFormDisabled" type="primary" @click="submitWarrantyClaim">Confirm</el-button>
      <el-button @click="
        dialogVisible_warranty_claim = false;
      claimRef.resetFields();
      ">Cancel</el-button>
    </div>

    <div style="height: 10px"></div>
    <el-form ref="claimRef" label-position="top" size="large" :disabled="claimFormDisabled" :model="warrantyClaimForm">
      <el-row :gutter="20">
        <!-- Failure Date and Circumstances -->
        <el-col :span="12">
          <el-form-item label="Failure Date" prop="failureDate"
            :rules="{ required: true, message: 'Failure Date is required', trigger: 'blur' }">
            <el-date-picker value-format="YYYY-MM-DD" format="YYYY-MM-DD" v-model="warrantyClaimForm.failureDate"
              type="date" placeholder="Pick a date" style="width: 100%"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Circumstances" prop="circumstances">
            <el-input v-model="warrantyClaimForm.circumstances"></el-input>
          </el-form-item>
        </el-col>

        <!-- Equipment Fault Description -->
        <el-col :span="24">
          <el-form-item label="Equipment Fault Description" prop="description"
            :rules="{ required: true, message: 'Equipment Fault Description is required', trigger: 'blur' }">
            <el-input type="textarea" v-model="warrantyClaimForm.description" rows="4"></el-input>
          </el-form-item>
        </el-col>

        <!-- Product Failure Pictures -->
        <el-col :span="24">
          <el-form-item prop="pictureList_warranty_claim" label="Product Failure Pictures"
            :rules="{ required: true, message: 'Product Failure Pictures is required', trigger: 'blur' }">
            <el-upload v-model:file-list="warrantyClaimForm.pictureList_warranty_claim" list-type="picture-card"
              :on-preview="handlePictureCardPreview" :on-remove="handleRemove" :before-upload="beforeAvatarUpload"
              accept=".png,.jpg" :auto-upload="false">
              <el-icon>
                <Plus />
              </el-icon>
            </el-upload>
          </el-form-item>
        </el-col>

        <!-- Dropdowns and Input Fields -->
        <el-col :span="6">
          <el-form-item label="Equipment/Engine" prop="brandId">
            <el-select disabled placeholder="Equipment/Engine" v-model="warrantyClaimForm.brandId" clearable
              @change="selectChange('brand', $event)">
              <el-option v-for="item in getSelectList('brand')" :key="item.id" :label="item.fileName"
                :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="Equipment Type" prop="eqId">
            <el-select disabled placeholder="Equipment Type" v-model="warrantyClaimForm.eqId" clearable
              @change="selectChange('eq', $event)">
              <el-option v-for="item in getSelectList('eq', warrantyClaimForm.brandId)" :key="item.id"
                :label="item.fileName" :value="item.id" clearable />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="Equipment Model" prop="modelId">
            <el-select disabled placeholder="Equipment Model" v-model="warrantyClaimForm.modelId"
              @change="selectChange('model', $event)" clearable>
              <el-option v-for="item in getSelectList('model', warrantyClaimForm.eqId)" :key="item.id"
                :label="item.fileName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- More Input Fields -->
        <el-col :span="6">
          <el-form-item label="Service Code" prop="versionId">
            <el-select disabled placeholder="Service Code" v-model="warrantyClaimForm.versionId"
              @change="selectChange('version', $event)" clearable>
              <el-option v-for="item in getSelectList(
                'version',
                warrantyClaimForm.modelId
              )" :key="item.id" :label="item.fileName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="serialNumber" label="Serial Number">
            <el-input disabled v-model="warrantyClaimForm.serialNumber"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="salesDate" label="Sales Date">
            <el-date-picker value-format="YYYY/MM/DD" format="YYYY/MM/DD" disabled v-model="warrantyClaimForm.salesDate"
              type="date" placeholder="Pick a date" style="width: 100%"></el-date-picker>
          </el-form-item>
        </el-col>

        <!-- Contact Information -->
        <el-col :span="8">
          <el-form-item prop="name" label="Name">
            <el-input disabled v-model="warrantyClaimForm.linkman"></el-input>
          </el-form-item>
        </el-col>
        <!--        <el-col :span="8">
          <el-form-item prop="companyName" label="Company Name">
            <el-input
              disabled
              v-model="warrantyClaimForm.companyName"
            ></el-input>
          </el-form-item>
        </el-col>-->
        <el-col :span="8">
          <el-form-item prop="phone" label="Phone">
            <el-input disabled v-model="warrantyClaimForm.phone"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="email" label="E-mail">
            <el-input disabled v-model="warrantyClaimForm.email"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="address" label="Business Street Address">
            <el-input disabled v-model="warrantyClaimForm.address"></el-input>
          </el-form-item>
        </el-col>

        <!-- Location Information -->
        <el-col :span="8">
          <el-form-item prop="city" label="City">
            <el-input disabled v-model="warrantyClaimForm.city"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="state" label="State">
            <el-input disabled v-model="warrantyClaimForm.state"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="country" label="Country">
            <el-input disabled v-model="warrantyClaimForm.country"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="zipCode" label="Zip/Postal Code">
            <el-input disabled v-model="warrantyClaimForm.postalCode"></el-input>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>

  </el-dialog>

  <!--客户信息弹窗-->
  <el-dialog title="Customers" v-model="dialogVisible_customer" style="margin-top: 50px" width="90%" height="90%">
    <el-row>
      <!-- 左侧布局：搜索框 + 客户列表 -->
      <el-col :span="8" style="background-color: #f5f5f5; padding: 12px 10px">
        <div style="padding: 12px 10px; background: #fff; margin-bottom: 6px">
          <el-input v-model="searchQuery_customer" placeholder="Name   Phone    E-Mail    Address..." clearable
            @input="searchCustomers">
            <template #suffix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>
        <!-- 客户信息列表 -->
        <div>
          <el-card style="margin-bottom: 6px; cursor: pointer" shadow="hover" v-for="(row, index) in customers"
            @click="selectCustomer(row)">
            <div class="infor-item">
              <div :title="row.linkman">{{ row.linkman }}</div>
              <div :title="row.city">{{ row.city }}</div>
            </div>
            <div class="infor-item">
              <div :title="row.phone">{{ row.phone }}</div>
              <div :title="row.companyName">{{ row.companyName }}</div>
            </div>
            <div class="infor-item">
              <div :title="row.email">{{ row.email }}</div>
              <div :title="row.address">{{ row.address }}</div>
            </div>
          </el-card>
          <div style="display: flex; justify-content: flex-end; margin-top: 8px">
            <el-pagination layout="prev, pager, next" :total="paramsCustomers.total"
              :page-size="paramsCustomers.pageSize" @current-change="currentPageChangeCustomers($event)"
              :current-page="paramsCustomers.pageNo" />
          </div>
        </div>
      </el-col>

      <!-- 右侧布局：表单 -->
      <el-col :span="16">
        <!-- 顶部按钮 -->
        <div style="
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
          ">
          <div>
            <el-button @click="deleteCustomer" :disabled="!formCustomer.id">Delete</el-button>
            <el-button @click="insertNewCustomer">+ Insert New</el-button>
            <el-button :disabled="formCustomer.id || issaveCustomer" @click="saveCustomer">Save</el-button>
          </div>
          <div>
            <el-button @click="cancelCustomer">Cancel</el-button>
            <el-button type="primary" @click="confirmCustomer">Confirm</el-button>
          </div>
        </div>

        <el-form :model="formCustomer" :disabled="false" class="customer-form" label-position="top" ref="customerForm">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="Name" required prop="linkman"
                :rules="{ required: true, message: 'Name is required', trigger: 'blur' }">
                <el-input v-model="formCustomer.linkman"></el-input>
              </el-form-item>
            </el-col>
            <!--            <el-col :span="12">
              <el-form-item label="Company name" prop="companyName">
                <el-input v-model="formCustomer.companyName"></el-input>
              </el-form-item>
            </el-col>-->
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="Phone" required prop="phone"
                :rules="{ required: true, message: 'Phone is required', trigger: 'blur' }">
                <el-input v-model="formCustomer.phone"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="E-mail" required prop="email"
                :rules="{ required: true, message: 'E-mail is required', trigger: 'blur' }">
                <el-input v-model="formCustomer.email"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="Business Street Address" prop="address"
                :rules="{ required: true, message: 'Address is required', trigger: 'blur' }">
                <el-input v-model="formCustomer.address"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="City" required prop="city"
                :rules="{ required: true, message: 'City is required', trigger: 'blur' }">
                <el-input v-model="formCustomer.city"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="State" prop="state">
                <el-input v-model="formCustomer.state"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="Country" prop="country">
                <el-input v-model="formCustomer.country"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="Zip/Postal code" prop="zipCode">
                <el-input v-model="formCustomer.postalCode"></el-input>
              </el-form-item>
            </el-col>
          </el-row>


        </el-form>
      </el-col>
    </el-row>
  </el-dialog>

  <el-dialog v-model="dialogImageVisible">
    <div style="display: flex; justify-content: center">
      <img w-full :src="dialogImageUrl" alt="Preview Image" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, nextTick } from "vue";
import BreadCrumb from "../../components/BreadCrumb.vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessageBox } from 'element-plus';
import {
  api_post_searchUserLinkMan,
  api_post_saveUserLinkMan,
  api_post_deleteUserLinkMan,
  api_post_saveRegisterProduct,
  api_post_deleteRegisterProduct,
  api_post_confirmRegisterProduct,
  api_post_saveClaimsRecord,
} from "../../utils/api";
import { Search } from "@element-plus/icons-vue";
import { dayjs, ElLoading } from "element-plus";
import { ElMessage, ClickOutside as vClickOutside } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
const base_downFileByPath_url = inject("$base_downFileByPath_url");
import {
  api_get_menu,
  api_post_registerlistPage,
  api_post_DeleteClaimsRecord
} from "../../utils/api";
const router = useRouter();
const loading = ref(false);
const dialogImageVisible = ref(false);
const registerFormRef = ref();
const selectedUserRef = ref();
const dialogImageUrl = ref("");

/*产品model bom 选择下拉框----下---- *//*产品model bom 选择下拉框----下---- */
/*产品model bom 选择下拉框----下---- *//*产品model bom 选择下拉框----下---- */
const brandId = ref("");
const eqId = ref("");
const modelId = ref("");
const versionId = ref("");
const menu = ref([]);
const params = ref({
  id: "",
  brand: "",
  productType: "",
  model: "",
  bom: "",
  startDate: null,
  endDate: null,
  total: 5,
  pageNo: 1,
  pageSize: 5,
});

const paramsCustomers = ref({
  total: 5,
  pageNo: 1,
  pageSize: 5,
});

const findNodeById = (tree, id) => {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].id === id) {
      return tree[i];
    }
    if (tree[i].children) {
      const found = findNodeById(tree[i].children, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
}

const getSelectList = (type, parentid) => {
  if (type === "brand") {
    return menu.value;
  }
  if (type === "eq") {
    const brandList = menu.value;
    return brandList.reduce((total, current) => {
      if (parentid) {
        if (current.id === parentid) {
          return [...total, ...(current.children || [])];
        }
      } else {
        return [...total, ...(current.children || [])];
      }
      return total;
    }, []);
  }

  if (type === "model") {
    const eqList = getSelectList("eq", brandId.value);
    return eqList.reduce((total, current) => {
      if (parentid) {
        if (current.id === parentid) {
          return [...total, ...(current.children || [])];
        }
      } else {
        return [...total, ...(current.children || [])];
      }
      return total;
    }, []);
  }

  if (type === "version") {
    const modelList = getSelectList("model", eqId.value);
    return modelList.reduce((total, current) => {
      if (parentid) {
        if (current.id === parentid) {
          return [...total, ...(current.children || [])];
        }
      } else {
        return [...total, ...(current.children || [])];
      }
      return total;
    }, []);
  }
};

const selectChange = (type, selectedValue) => {
  switch (type) {
    case "brand":
      eqId.value = "";
      modelId.value = "";
      versionId.value = "";
      if (selectedValue) {
        brandId.value = selectedValue;
        params.value.brand = findNodeById(menu.value, selectedValue).fileName;
      } else {
        params.value.brand = "";
      }

      params.value.productType = "";
      params.value.model = "";
      params.value.bom = "";

      break;
    case "eq":
      modelId.value = "";
      versionId.value = "";

      if (selectedValue) {
        eqId.value = selectedValue;
        params.value.productType = findNodeById(menu.value, selectedValue).fileName;
      } else {
        params.value.productType = "";
      }

      params.value.model = "";
      params.value.bom = "";
      break;
    case "model":
      versionId.value = "";

      if (selectedValue) {
        modelId.value = selectedValue;
        params.value.model = findNodeById(menu.value, selectedValue).fileName;
      } else {
        params.value.model = "";
      }
      params.value.bom = "";
      break;
    case "version":
      if (selectedValue) {
        versionId.value = selectedValue;
        params.value.bom = findNodeById(menu.value, selectedValue).fileName;
      } else {
        params.value.bom = "";
      }

      break;
  }

  params.value.id =
    versionId.value || modelId.value || eqId.value || brandId.value;

  //这里重新赋值---注意
  //这里重新赋值---注意
  //这里重新赋值，可以使得如果重新选择品牌brand后面的值跟着清空
  registerForm.value.brand = params.value.brand;
  registerForm.value.productType = params.value.productType;
  registerForm.value.model = params.value.model;
  registerForm.value.bom = params.value.bom;

  registerForm.value.brandId = brandId.value;
  registerForm.value.eqId = eqId.value;
  registerForm.value.modelId = modelId.value;
  registerForm.value.versionId = versionId.value;


};
/*产品model bom 选择下拉框----上---- *//*产品model bom 选择下拉框----上---- */
/*产品model bom 选择下拉框----上---- *//*产品model bom 选择下拉框----上---- */



/**
 * 列表相关
 */
const searchForm = ref({
  name: "",
  phone: "",
  startDate: null,
  endDate: null,
  status: "",
  hideInvalid: false,
});

const itemsTable = ref([]);

const getList = () => {

  let loading;
  loading = ElLoading.service({
    lock: true,
    text: "Loading...",
    background: "rgba(255, 255, 255, 0.7)",
  });

  var queryRegister = { ...params.value, ...searchForm.value }
  // Fetch list logic
  api_post_registerlistPage(queryRegister).then((result) => {

    itemsTable.value = result.data;
    params.value.pageNo = result.pageNo;
    params.value.total = result.total;
    console.log(result.data);

  }).finally(() => {
    loading.close();
  });
};

const getDetail = (item) => {
  const isEdit = item.state === "1";
  registerFormDisabled.value = !isEdit;
  showRegisterDialog.value = true;

  setTimeout(() => {
    !isEdit && registerFormRef.value.clearValidate();
  });

  //保证resetFields函数可以重置数据
  nextTick(() => {


    var fileArray = [];
    if (item.files) {
      const fileList = item.files.split(",");
      fileList.forEach((f, index) => {
        var currentDomain = window.location.origin;
        // 拼接完整的链接
        var imageUrl = currentDomain + "/fileviewer/" + f;
        // 创建一个模拟的文件对象
        const file = {
          name: 'image.jpg', // 图片文件名
          url: imageUrl, // 图片路径
          status: 'finished', // 设置状态为已上传
        };
        fileArray.push(file);

      });
    }

    item.pictureList_receipt = fileArray;
    registerForm.value = { ...item };
    selectedUserForm.value = { ...item };

  });
};

const confirm = (item) => {
  api_post_confirmRegisterProduct({ id: item.id }).then((res) => {
    if (res.code == 0) {
      item.status = "2";
      ElMessage.success("Successfully!")
    } else {
      ElMessage.error("Failed!")
    }

  })
};

const invalid = (item) => {

  ElMessageBox.confirm('Are you sure you want to invalid this product registration?', 'Confirm', {
    confirmButtonText: 'OK',
    cancelButtonText: 'Cancel',
    type: 'warning',
  }).then(() => {
    api_post_deleteRegisterProduct({ id: item.id }).then((res) => {
      if (res.code == 0) {
        ElMessage.success("Successfully!")
        getList();
      } else {
        ElMessage.error("Failed!")
      }

    })
  }).catch(() => {
    // 用户点击了取消按钮
    ElMessage.info('Operation canceled');
  });

};

const invalidItem = (item) => {

  ElMessageBox.confirm('Are you sure you want to invalid this claim registration?', 'Confirm', {
    confirmButtonText: 'OK',
    cancelButtonText: 'Cancel',
    type: 'warning',
  }).then(() => {
    api_post_DeleteClaimsRecord({ id: item.id }).then((res) => {
      if (res.code == 0) {
        ElMessage.success("Successfully!")
        getList();
      } else {
        ElMessage.error("Failed!")
      }

    })
  }).catch(() => {
    // 用户点击了取消按钮
    ElMessage.info('Operation canceled');
  });

}

const isWithin24Hours = (dateString) => {
  const currentDate = new Date();
  const givenDate = new Date(dateString);
  const timeDifference = currentDate - givenDate
  return timeDifference <= 24 * 60 * 60 * 1000;
}




/******************列表相关**********************/

/**
 * 新增列表数据弹窗相关
 */

const showRegisterDialog = ref(false);
const registerFormDisabled = ref(false);
const registerForm = ref({
  id: "",
  brandId: "",
  eqId: "",
  modelId: "",
  versionId: "",
  brand: "",
  productType: "",
  model: "",
  bom: "",
  serialNumber: "",
  salesDate: "",
  description: "",
  pictureList_receipt: [],
  companyLinkmanId: "",
  linkman: "",
  phone: "",
  email: "",
  address: "",
  city: "",
  state: "",
  country: "",
  postalCode: "",

});
const selectedUserForm = ref({
  id: "",
  linkman: "",
  companyName: "",
  phone: "",
  email: "",
  address: "",
  city: "",
  state: "",
  country: "",
  postalCode: "",
});

/**/

const registerNewProduct = () => {
  showRegisterDialog.value = true;
  registerFormDisabled.value = false;
  for (const key in registerForm.value) {
    registerForm.value[key] = "";
  }
  for (const key in selectedUserForm.value) {
    selectedUserForm.value[key] = "";
  }

};

const registerDialogBeforClose = (done) => {
  registerFormRef.value.resetFields();
  selectedUserRef.value.resetFields();
  done();
};



const beforeAvatarUpload = (rawFile) => {
  console.log(rawFile.type, "rawFile.type");
  if (!["image/jpeg", "image/png"].includes(rawFile.type)) {
    ElMessage.error("Please select pictures");
    return false;
  } else if (rawFile.size / 1024 / 1024 > 4) {
    ElMessage.error("picture size can not exceed 2MB!");
    return false;
  }
  return true;
};
const handlePictureCardPreview = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!;
  dialogImageVisible.value = true;
};

const submitRegister = () => {
  registerFormRef.value.validate((valid) => {
    if (valid) {
      console.log(registerForm.value, selectedUserForm.value.id); //提交给服务端的数据
      if (!selectedUserForm.value.id) {
        ElMessage.error("Please Select Customer!");
        return;
      }

      var formData = new FormData();

      //在解构赋值前处理一下数据
      registerForm.value.companyLinkmanId = selectedUserForm.value.id;
      //排除selectedUserForm中的id,不能将id结构到对象中，影响后端新增或者修改的判断
      //排除createDate,updateDate后端controller---自动赋值格式异常------自动赋值格式异常---
      const { id, createDate: _, updateDate: __, ...selectedUserFormNoSomefield } = selectedUserForm.value;
      const { createDate: ___, updateDate: ____, ...registerFormNoSomefield } = registerForm.value;
      //解构值，顺序不能变，后面解构的值，覆盖前面字段的值
      var data = { ...registerFormNoSomefield, ...selectedUserFormNoSomefield };

      console.log("registerForm", registerForm.value);
      console.log("selectedUserFormNoSomefield", selectedUserFormNoSomefield);
      console.log("data", data);

      // 将每个属性和值添加到 formData 中
      for (var key in data) {
        if (data.hasOwnProperty(key)) {
          formData.append(key, data[key]); // 将每个属性和值添加到 formData 中
        }
      }
      if (Array.isArray(registerForm.value.pictureList_receipt)) {
        registerForm.value.pictureList_receipt.forEach((file, index) => {
          formData.append(`file`, file.raw);
        });
      }

      api_post_saveRegisterProduct(formData).then((res) => {
        if (res.code == 0) {
          ElMessage.success("Successfully!")
          getList();
          showRegisterDialog.value = false;

          for (var key in registerForm.value) {
            if (registerForm.value.hasOwnProperty(key)) {
              registerForm.value[key] = "";
            }
          }

          for (var key in selectedUserForm.value) {
            if (selectedUserForm.value.hasOwnProperty(key)) {
              selectedUserForm.value[key] = "";
            }
          }
        } else {
          ElMessage.error("Failed!")
        }

      })



    }
  });
};

/******************新增列表数据弹窗相关**********************/

/**
 * 用户信息弹窗相关方法
 */

const customerForm = ref();
const dialogVisible_customer = ref(false);
const searchQuery_customer = ref("");
const formCustomer = ref({
  id: "",
  linkman: "",
  company: "",
  phone: "",
  email: "",
  address: "",
  city: "",
  state: "",
  country: "",
  postalCode: "",
  laborRate: "",
});

const customers = ref([
  // 更多客户信息
]);

const searchCustomers = () => {

  //调用接口
  api_post_searchUserLinkMan({ ...paramsCustomers.value, keywords: searchQuery_customer.value }).then((res) => {
    if (res) {
      customers.value = res.data;
      paramsCustomers.value.pageNo = res.pageNo;
      paramsCustomers.value.total = res.total;
    }
  });

};

const currentPageChangeCustomers = (currentPage) => {
  paramsCustomers.value.pageNo = currentPage;
  searchCustomers();

};

const selectCustomer = (customer) => {
  formCustomer.value = { ...customer };
};

const deleteCustomer = () => {
  const id = formCustomer.value.id;

  ElMessageBox.confirm('Please Confirm', 'Delete', {
    confirmButtonText: 'Confirm',
    cancelButtonText: 'Cancel',
    type: 'warning'
  }).then(() => {
    // 用户点击了“确定”按钮
    // 调用删除接口，成功之后执行下面代码
    api_post_deleteUserLinkMan(formCustomer.value);

    insertNewCustomer();
    const index = customers.value.findIndex((item) => item.id === id);
    index >= 0 && customers.value.splice(index, 1);
  }).catch(() => {
    // 用户点击了“取消”按钮

  });



};



const insertNewCustomer = () => {
  // if (!customerForm) return;
  //customerForm.value.resetFields();
  for (const key in formCustomer.value) {
    formCustomer.value[key] = "";
  }

};

const issaveCustomer = ref(false);
const saveCustomer = () => {
  customerForm.value.validate((valid) => {
    if (valid) {
      issaveCustomer.value = true;
      if (!formCustomer.value.id) {
        //设置上后端不会转换布尔类型异常
        formCustomer.value.isNewRecord = true;
      } else {
        formCustomer.value.isNewRecord = false;
      }
      // 保存客户信息的逻辑
      console.log("保存客户信息: ", formCustomer.value);
      //保存成功后调用查询列表接口
      api_post_saveUserLinkMan(formCustomer.value).then((res) => {
        issaveCustomer.value = false;
        if (res) {
          if (!formCustomer.value.id) {
            formCustomer.value.id = res;
            console.log(formCustomer.value);
          }
          searchCustomers();
          ElMessage.success("Successfully");
        }
      });
    }
  });

};
const dialogVisible_customerFunc = () => {
  dialogVisible_customer.value = true;
  insertNewCustomer();
  searchCustomers();
}


const cancelCustomer = () => {
  insertNewCustomer();
  dialogVisible_customer.value = false;
};

const confirmCustomer = () => {

  customerForm.value.validate((valid) => {
    if (valid) {
      console.log(formCustomer.value);
      if (formCustomer.value.id) {
        selectedUserForm.value = { ...formCustomer.value };
        dialogVisible_customer.value = false;

      } else {
        ElMessage.warning("Please save first!");
      }
    } else {
    }
  });

};

/**
 * 维修弹窗相关
 */
const dialogVisible_warranty_claim = ref(false);
const claimFormDisabled = ref(false);
const claimRef = ref();
const warrantyClaimForm = ref({
  registerProductId: "",
  failureDate: "",
  circumstances: "",
  description: "",
  pictureList_warranty_claim: [],
  brand: "",
  type: "",
  model: "",
  serviceCode: "",
  serialNumber: "",
  salesDate: "",
  name: "",
  companyName: "",
  phone: "",
  email: "",
  address: "",
  city: "",
  state: "",
  country: "",
  postalCode: "",
});

const claimDialogBeforClose = (done) => {
  claimRef.value.resetFields();
  done();
};

const addWarrantyRecord = (item) => {
  dialogVisible_warranty_claim.value = true;
  claimFormDisabled.value = false;
  /*.claimRef.value.resetFields(）*/
  //保证resetFields函数可以重置数据
  nextTick(() => {
    warrantyClaimForm.value = { ...item };
    //description字段重复重置
    warrantyClaimForm.value.description = "";
    //add new
    warrantyClaimForm.value.id = "";
    warrantyClaimForm.value.registerProductId = item.id;
  });

};

const detailWarrantyRecord = (item, data) => {
  console.log("Detail Warranty Record");
  dialogVisible_warranty_claim.value = true;
  claimFormDisabled.value = true;
  nextTick(() => {

    warrantyClaimForm.value = { ...data, ...item };


    var fileArray = [];
    if (data.files) {
      const fileList = data.files.split(",");
      fileList.forEach((f, index) => {
        var currentDomain = window.location.origin;
        // 拼接完整的链接
        var imageUrl = currentDomain + "/fileviewer/" + f;
        // 创建一个模拟的文件对象
        const file = {
          name: 'image.jpg', // 图片文件名
          url: imageUrl, // 图片路径
          status: 'finished', // 设置状态为已上传
        };
        fileArray.push(file);

      });
    }

    warrantyClaimForm.value.pictureList_warranty_claim = fileArray;

    //detail
    warrantyClaimForm.value.id = data.id;
    warrantyClaimForm.value.registerProductId = item.id;
    claimRef.value.clearValidate();
  });
};

const submitWarrantyClaim = () => {
  claimRef.value.validate((valid) => {
    if (valid) {

      var formData = new FormData();

      var data = { ...warrantyClaimForm.value };
      // 将每个属性和值添加到 formData 中
      for (var key in data) {
        if (data.hasOwnProperty(key)) {
          formData.append(key, data[key]); // 将每个属性和值添加到 formData 中
        }
      }

      if (Array.isArray(warrantyClaimForm.value.pictureList_warranty_claim)) {
        warrantyClaimForm.value.pictureList_warranty_claim.forEach((file, index) => {
          formData.append(`file`, file.raw);
        });
      }

      api_post_saveClaimsRecord(formData).then((res) => {
        if (res.code == 0) {
          ElMessage.success("Successfully!")
          //调用接口接口成功后执行后续代码
          claimRef.value.resetFields();
          dialogVisible_warranty_claim.value = false;
          claimRef.value.resetFields();
          getList();
        } else {
          ElMessage.error("Failed!")
        }

      })


    }
  });
};

const currentPageChange = (currentPage) => {
  params.value.pageNo = currentPage;
  getList();

};



onMounted(() => {

  getList();
  api_get_menu().then((res) => {
    menu.value = res;
  });

});
</script>

<style lang="less" scoped>
.container-view {
  display: flex;
}

.sidebar {
  width: 300px;
  padding: 20px 8px;
  background-color: #f7f7f7;
  border-top: none; // 确保没有边框
  margin-top: 0; // 确保没有外边距
  box-sizing: border-box;

  .side-form {
    background: #fff;
    padding: 10px;
  }
}

.content {
  flex: 1;
  padding: 20px;
  box-sizing: border-box;
}

.button-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* 居中对齐 */
  gap: 10px;
  /* 按钮之间的间距 */
  background-color: #fff;
  margin-bottom: 16px;
  padding: 10px;
}

.infor-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;

  >div {
    flex: 1;
    white-space: nowrap;
    /* 确保文本在一行内显示 */
    overflow: hidden;
    /* 隐藏超出容器宽度的文本 */
    text-overflow: ellipsis;
    /* 使用省略号表示被截断的文本 */
  }

  >div:nth-of-type(2n + 1) {
    padding-right: 5px;
  }

  >div:nth-of-type(2n) {
    padding-left: 5px;
  }
}

.equipment-item {
  color: rgb(250, 181.5, 181.5);
  margin-bottom: 4px;
}

.status-invalid {
  color: red;
}

.status-confirm {
  color: blue;
}

.status-bar {
  width: 5px;
  background-color: gray;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
}

.status-invalid-bar {
  background-color: red;
}

.status-confirm-bar {
  background-color: blue;
}

.ellipsis {
  color: gray;
}

.el-dialog__header {
  font-weight: bold;
}

.dialog-footer {
  text-align: right;
}

.custom-top-confirm {
  position: absolute;
  top: 10px;
  /* 调整距离顶部的距离 */
  right: 40px;
  /* 调整距离右侧的距离 */
}

.customer-form {
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background-color: white;
}

.dialog-footer {
  text-align: right;
}

.el-button {
  margin-right: 10px;
}

.radio-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 50px;
  height: 25px;
}

.color-block {
  width: 32px;
  height: 10px;

  &.red {
    background: #f56c6c;
  }

  &.grey {
    background: rgb(177.3, 179.4, 183.6);
  }

  &.blue {
    background: #409eff;
  }
}

.add-button {
  height: 60px;
  padding: 0 5px;
  width: 20px;
}

/**/
/* 覆盖 Element Plus  */
</style>
