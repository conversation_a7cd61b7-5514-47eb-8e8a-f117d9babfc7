/*
 * @Author: <PERSON>
 * @Date: 2024-06-26 13:48:54
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-07-10 14:05:07
 */
import {
  createRouter,
  createWebHashHistory,
  createWebHistory,
} from "vue-router";

const routes = [
  {
    path: "/",
    name: "Layout",
    component: () => import("../components/Layout.vue"),
    // redirect: '/test',
    redirect: "/login",
    children: [
      {
        path: "/catalog",
        name: "Catalog",
        meta: {
          keepAlive: true, //添加这个作为标志符，表明该页面需要保留状态
        },
        component: () => import("../pages/catalog/index.vue"),
      },
      {
        path: "/home",
        name: "Home",
        meta: {
          keepAlive: true, //添加这个作为标志符，表明该页面需要保留状态
        },
        component: () => import("../pages/home/<USER>"),
      },
      {
        path: "/product",
        name: "Product",
        meta: {
          keepAlive: true, //添加这个作为标志符，表明该页面需要保留状态
        },
        component: () => import("../pages/product/index.vue"),
      },
      {
        path: "/detail",
        name: "Detail",
        meta: {
          keepAlive: true, //添加这个作为标志符，表明该页面需要保留状态
        },
        component: () => import("../pages/detail/index.vue"),
      },
      {
        path: "/cart",
        name: "Cart",
        meta: {
          keepAlive: true, //添加这个作为标志符，表明该页面需要保留状态
        },
        component: () => import("../pages/cart/index.vue"),
      },
      {
        path: "/favor",
        name: "favor",
        meta: {
          keepAlive: true, //添加这个作为标志符，表明该页面需要保留状态
        },
        component: () => import("../pages/cart/index.vue"),
      },
      {
        path: "/orderList",
        name: "OrderList",
        meta: {
          keepAlive: true, //添加这个作为标志符，表明该页面需要保留状态
        },
        component: () => import("../pages/orderList/index.vue"),
      },
      {
        path: "/createOrder",
        name: "CreateOrder",
        meta: {
          keepAlive: false, //添加这个作为标志符，表明该页面需要保留状态
        },
        component: () => import("../pages/createOrder/index.vue"),
      },
      {
        path: "/warranty",
        name: "Warranty",
        meta: {
          keepAlive: false, //添加这个作为标志符，表明该页面需要保留状态
        },
        component: () => import("../pages/warranty/index.vue"),
      },
      {
        path: "/notice",
        name: "Notice",
        meta: {
          keepAlive: false, //添加这个作为标志符，表明该页面需要保留状态
        },
        component: () => import("../pages/notice/index.vue"),
      },
    ],
  },
  {
    path: "/login",
    name: "Login",
    meta: {
      keepAlive: true, //添加这个作为标志符，表明该页面需要保留状态
    },
    component: () => import("../pages/login/index.vue"),
  },
  {
    path: '/orderDetailView',
    name: "orderDetailView",
    meta: { keepAlive: false },
    component: () => import("../pages/orderDetailView/index.vue")
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
