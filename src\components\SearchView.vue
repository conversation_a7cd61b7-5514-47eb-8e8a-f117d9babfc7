<template>
  <div class="search-view-wrapper">
    <div class="search-view">
      <div class="search-view-content">
        <div class="search-input-view">
          <div class="search-input-view-content">
            <el-button type="primary" class="search-btn" @click="productSearch">
              Query The Equipment
            </el-button>
            <el-input style="width: 70%" placeholder="Search By Service_Code ,Model,Serial_Number,Whole_machine_Code"
              v-model="productKey" clearable />
          </div>
          <div class="search-input-view-content">
            <el-button type="primary" class="search-btn" @click="partSearch">
              Query The Part
            </el-button>
            <el-input placeholder="Search By Part Number" clearable style="width: 300px" v-model="partKey" />
          </div>
        </div>
        <div class="cart-list" v-if="showPartTabel">
          <div class="head-all-item small">
            <div class="item-all-image" style="width: 140px; padding-left: 16px">
              Equipment
            </div>
            <div style="
                display: flex;
                align-items: center;
                padding-left: 16px;
                flex: 1;
              ">
              <div class="head-all-item-view materierCode">MaterielCode</div>
              <div class="head-all-item-view quantity">Name_Chinese</div>
              <div class="head-all-item-view quantity">Name_English</div>
              <div class="head-all-item-view unitPrice">Price</div>
               <div class="head-all-item-view operate">Operate</div>
            </div>
          </div>
          <el-scrollbar ref="scrollbarRef" max-height="330px" always>
            <div>
              <div class="shopping-cart-item-all" v-for="(commodityItem, index) in partData">
                <div class="item-all-name small">
                  {{ commodityItem.materielCode }}
                </div>
                <div style="display: flex; align-items: flex-start">
                  <div style="min-width: 140px; max-width: 140px; padding: 0 10px">
                    <el-image class="wb-image" :src="getImageUrl(commodityItem.productFileList || [])[0] ||
                      ''
                      " :preview-src-list="getImageUrl(commodityItem.productFileList || [], 'all')
                        " style="width: 100%; height: auto" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2"
                      :initial-index="4" fit="cover" />
                  </div>
                  <div class="item-all-content" style="flex: 1; min-height: 130px">
                    <div class="cart-all-item" @click="jumpPart(commodityItem, item)"
                      v-for="(item, index) in commodityItem.spartList || []">
                      <div class="cart-all-item-view materierCode">
                        {{ item.materielCode }}
                      </div>
                      <div class="cart-all-item-view quantity">
                        {{ item.materielName }}
                      </div>
                      <div class="cart-all-item-view quantity">
                        {{ item.materielNameEn }}
                      </div>
                      <div class="cart-all-item-view unitPrice">
                        ￥{{ item.price }}
                      </div>
                      <div class="cart-all-item-view operate">
                        <el-button link type="primary" size="small" @click.prevent="addCart(item,'part')">
                          <img src="../assets/images/main/purCar.svg" alt="+" height="15px" />
                        </el-button>
                        <el-button link type="primary" size="small" @click.prevent="addFavorit(item,'part')">
                          <img src="../assets/images/main/favorites.svg" alt="+" height="15px" />
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="partData.length <= 0" style="text-align: center; margin-top: 20px; color: #a8abb2">
                暂无数据
              </div>
            </div>
          </el-scrollbar>
        </div>

        <div class="cart-list" v-if="!showPartTabel">
          <div class="head-all-item small">
            <div class="item-all-image" style="width: 140px; padding-left: 16px">
              From The Machine
            </div>
            <div class="head-all-item-view materierCode">MaterielCode</div>
            <div class="head-all-item-view quantity">amount</div>
            <div class="head-all-item-view quantity">Name_Chinese</div>
            <div class="head-all-item-view quantity">Name_English</div>
            <div class="head-all-item-view unitPrice">unit_Price</div>
            <div class="head-all-item-view operate">Operate</div>
          </div>
          <el-scrollbar ref="scrollbarRef" max-height="330px" always>
            <div>
              <div class="shopping-cart-item-all">
                <div>
                  <div class="item-all-content">
                    <div class="cart-all-item" @click="jumpProduct(item)" v-for="(item, index) in tableData || []">
                      <div style="min-width: 140px; max-width: 140px">
                        <el-image class="wb-image" :src="getImageUrl(item.productFileList || [])[0] || ''
                          " :preview-src-list="getImageUrl(item.productFileList || [], 'all')
                            " style="width: 80px; height: auto" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2"
                          :initial-index="4" fit="cover" />
                      </div>
                      <div class="cart-all-item-view materierCode">
                        {{ item.materielCode }}
                      </div>
                      <div class="cart-all-item-view quantity">
                        {{ item.amount }}
                      </div>
                      <div class="cart-all-item-view quantity">
                        {{ item.materielName }}
                      </div>
                      <div class="cart-all-item-view quantity">
                        {{ item.materielNameEn }}
                      </div>
                      <div class="cart-all-item-view unitPrice">
                        ￥{{ item.price }}
                      </div>
                      <div class="cart-all-item-view operate">
                        <el-button link type="primary" size="small" @click.prevent="addCart(item,'product')">
                          <img src="../assets/images/main/purCar.svg" alt="+" height="15px" />
                        </el-button>
                        <el-button link type="primary" size="small" @click.prevent="addFavorit(item,'product')">
                          <img src="../assets/images/main/favorites.svg" alt="+" height="15px" />
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="tableData.length <= 0" style="text-align: center; margin-top: 20px; color: #a8abb2">
                暂无数据
              </div>
            </div>
          </el-scrollbar>
        </div>

        <!-- <el-table :data="tableData" style="width: 100%" v-if="!showPartTabel">
          <el-table-column prop="Order" label="Order" />
          <el-table-column prop="service_code" label="service_code" />
          <el-table-column prop="seriel_number" label="seriel_number" />
          <el-table-column
            prop="whole_machine_code"
            label="whole_machine_code"
          />
        </el-table>
        <el-table :data="partData" style="width: 100%" v-if="showPartTabel">
          <el-table-column prop="Order" label="Order" />
          <el-table-column prop="service_code" label="service_code" />
          <el-table-column prop="seriel_number" label="seriel_number" />
          <el-table-column
            prop="whole_machine_code"
            label="whole_machine_code"
          />
          <el-table-column prop="part_number" label="part_number" />
          <el-table-column prop="Name_China" label="Name_China" />
          <el-table-column prop="Name_English" label="Name_English" />
          <el-table-column prop="Unit_price" label="Unit_price" />
        </el-table> -->
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {ref, onBeforeMount, inject} from "vue";
import { api_post_spartPage, api_post_productPage
  ,api_post_addToFavorites
  ,api_post_addToCart,} from "../utils/api";
import {ElLoading, ElMessage} from "element-plus";
const tableData = ref<any[]>([]);
const partData = ref<any[]>([]);
const showPartTabel = ref<boolean>(false);
const productKey = ref<string>("");
const partKey = ref<string>("");
const base_downFileByPath_url = inject("$base_downFileByPath_url");
const getTableList = (type: string) => {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(255, 255, 255, 0.7)",
  });
  if (type == "part") {
    api_post_spartPage({
      key: partKey.value,
      pageSize: 100,
    })
      .then((res) => {
        partData.value = res.data || [];
      })
      .finally(() => {
        loading.close();
      });
  } else {
    api_post_productPage({
      key: productKey.value,
      pageSize: 100,
    })
      .then((res) => {
        tableData.value = res.data || [];
      })
      .finally(() => {
        loading.close();
      });
  }
};
const productSearch = () => {
  showPartTabel.value = false;
  getTableList("product");
};

const partSearch = () => {
  showPartTabel.value = true;
  getTableList("part");
};

/**
 *
 * @param commodityItem  产品信息
 * @param item 配件信息
 */
const jumpPart = (commodityItem, item) => {
  console.log("jumpPart");
};

const jumpProduct = (commodityItem) => {
  console.log("jumpProduct");
};

const getImageUrl = (array, type) => {
  const data = array.reduce((total: string[], current: string) => {
    const urlArray = current.split("/").reverse();
    const isProduct = type !== "all" ? urlArray[0].includes("product") : true;
    if (
      urlArray.length > 0 &&
      isProduct &&
      (urlArray[0].includes(".jpg") || urlArray[0].includes(".png"))
    ) {
      total.push(base_downFileByPath_url + current);
    }
    return total;
  }, []);
  return data;
};

const addCart = (row,productType) => {
  console.log(row);
  api_post_addToCart([
    {
      amount: 1,
      bomItemsId: row.id,
      productType: productType,
    },
  ]).then((res) => {
    console.log(res);
    if (res) {
      ElMessage.success({
        message: res.msg,
        duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
        offset: 150,
      });
    }
  });
};

const addFavorit = (row,productType) => {
  api_post_addToFavorites([
    {
      bomItemsId: row.id,
      productType: productType,
    },
  ]).then((res) => {
    if (res) {

      ElMessage.success({
        message: res.msg,
        duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
        offset: 150,
      });
    }

  });
};
</script>
<style scoped lang="less">
.search-view-wrapper {
  position: absolute;
  background-color: #fff;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  z-index: 1;
  width: 100%;
  top: 53px;
  left: 0;
  padding: 10px;
  z-index: 20;
  transition: height 0.5s ease-out;
  min-height: 500px;
  max-height: 500px;
  overflow-y: auto;
}

.search-view {
  .search-view-content {
    .search-input-view {
      display: flex;
      margin-bottom: 16px;

      .search-input-view-content {
        display: flex;
        align-items: center;
        width: 46%;

        .search-btn {
          margin-right: 10px;
        }
      }
    }
  }
}

.cart-list {
  color: #213547;
  font-size: 12px;

  .head-all-item {
    height: 39px;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border: 1px solid #e8e8e8;
    margin: 10px 0;

    // padding-left: 16px;
    &.small {
      height: 32px;
      font-size: 12px;
    }

    &-view {
      &.checkbox {
        width: 64px;
      }

      &.name {
        flex: 3;
        margin-right: 16px;
      }

      &.materierCode {
        flex: 2;
        margin-right: 16px;
      }

      &.quantity {
        flex: 3;
        margin-right: 16px;
      }

      &.unitPrice {
        flex: 1;
        margin-right: 16px;
      }

      &.remarks {
        flex: 3;
        margin-right: 16px;
      }

      &.operate {
        margin-right: 0;
        width: 100px;
      }
    }
  }

  .shopping-cart-item-all {
    border-radius: 8px;
    margin-bottom: 16px;

    .item-all-name {
      align-items: center;
      background-color: #f3f6f8;
      border: 1px solid #f0f3f5;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      display: flex;
      flex-direction: row;
      height: 48px;
      overflow: hidden;
      padding-left: 16px;
      color: #11192d;
      font-size: 14px;
      font-weight: 500;

      &.small {
        height: 32px;
        font-size: 12px;
      }
    }

    .item-all-checkbox {
      margin-right: 16px;
    }

    .item-all-content {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-color: #f0f3f5;
      border-style: solid;
      border-width: 1px;
      position: relative;
      padding-left: 16px;
      word-break: break-all;

      .cart-all-item {
        align-items: flex-start;
        display: flex;
        position: relative;
        align-items: center;
        padding: 8px 0;
        cursor: pointer;

        &:hover {
          background-color: #f5f5f5;
        }

        .item-all-checkbox {
          width: 48px;
        }

        .cart-all-item-info {
          flex: 3;
          align-items: center;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          margin-right: 16px;
          padding-top: 2px;

          .cart-all-item-img {
            width: 46px;
            height: 46px;
            margin-right: 12px;
            background-color: rgba(0, 0, 0, 0.02);
            position: relative;
          }

          .cart-all-item-name {
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            color: #11192d;
            display: -webkit-box;
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            margin-bottom: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .cart-all-item-view {
          margin-right: 16px;

          &.materierCode {
            flex: 2;
          }

          &.quantity {
            flex: 3;
          }

          &.unitPrice {
            flex: 1;
            color: #ff5000;
            font-size: 18px;
            font-weight: 600;
          }

          &.remarks {
            flex: 3;
          }

          &.operate {
            margin-right: 0;
            width: 120px;

            .operate-item {
              display: block;
            }
          }
        }
      }
    }
  }
}

.wb-image{
  /deep/ .el-image-viewer__canvas{
  .el-image-viewer__img{
    background: #fff;
  }
}
}
</style>
