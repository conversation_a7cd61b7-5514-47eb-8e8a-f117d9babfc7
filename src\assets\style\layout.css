* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}



body {
  font-family: Arial, sans-serif;
}

.header {
  background-color: #333;
  color: white;
  padding: 10px;
  text-align: center;
}

.container {
  display: flex;
  /* height: calc(100vh - 146px); */
  min-height: 100vh;
  padding: 12px;
}

.sidebar {
  min-width: 300px;
  max-width: 700px;
  width: 300px;
  background-color: #fafaf5;
  border-top: #dddddd solid 3px;
}

#sidebar-resizer {
  width: 5px;
  cursor: e-resize;
  background-color: #ddd;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 11;
}

#sidebar-resizer:active {
  background-color: #46557c;
}

.main {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding: 10px;
}


.menu {
  list-style: none;
  background-color: #fafaf5;
}

.menu-item {
  margin-bottom: 10px;
}

.menu-icon {
  margin-right: 10px;
  width: 14px;
  height: 14px;
}

.arrow-icon {
  margin-left: auto;
  width: 14px;
  height: 14px;
}

.menu-title {
  display: block;
  padding: 5px;
  text-decoration: none;
  color: #333;
  cursor: pointer;
  position: relative;
  text-wrap: nowrap;
  overflow: hidden;
  font-size: 14px;
}

.menu-title.level-2-active {
  color: red;
}

.menu-title.level-3-active {
  background-color: #60666e;
  color: white;
}

.menu-title.level-4-active::after {
  content: '';
  display: block;
  width: 100%;
  height: 2px;
  background-color: red;
  position: absolute;
  bottom: 0;
  left: 0;
}

.submenu {
  display: none;
  list-style: none;
  padding-left: 10px;
}

.submenu li {
  margin-bottom: 5px;
}

.submenu a {
  text-decoration: none;
  color: #333;
  position: relative;
}

.submenu a:hover {
  color: red;
}

.submenu a:hover::after {
  content: '';
  display: block;
  width: 100%;
  height: 2px;
  /* background-color: red; */
  position: absolute;
  bottom: 0;
  left: 0;
  margin-bottom: 2px;
}

.resizer {
  width: 5px;
  cursor: ew-resize;
  background-color: #ddd;
  position: relative;
  z-index: 1;
}

.menu-title.selected-file {
  color: red;
  position: relative;
}

.menu-title.selected-file::after {
  content: '';
  display: block;
  width: 100%;
  height: 2px;
  position: absolute;
  bottom: 0;
  left: 0;
}