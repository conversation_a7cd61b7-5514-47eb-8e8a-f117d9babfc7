<template>
  <div style="position: fixed; top: 53px; width: 100%; z-index: 100">
    <BreadCrumb />
  </div>

  <div class="container" style="padding-top: 102px; height: 100vh">
    <aside class="sidebar" id="sideBar" style="height: 100%">
      <!-- <ul class="menu root-menu" id="menu"></ul> -->
      <recursive-menu
        style="height: 100%"
        :menuData="menuData"
        :defaultOpeneds="defaultOpeneds"
        v-if="menuData.length !== 0"
      />
    </aside>
    <div class="resizer" id="sidebar-resizer"></div>
    <main class="main">
      <!--  配件canvas页面   -->
      <div v-show="Src.includes('.png') && Src.length !== 0">
        <div id="controls">
          <div
            class="controls-item"
            id="zoom-out"
            title="narrow"
            @click="wheelDown"
          >
            <img src="../../assets/images/main/minus.svg" alt="-" />
          </div>
          <div
            class="controls-item"
            id="zoom-in"
            title="enlarge"
            @click="wheelUp"
          >
            <img src="../../assets/images/main/add.svg" alt="+" />
          </div>
          <div class="controls-item" id="">
            <img
              src="../../assets/images/main/max_screen.svg"
              alt="+"
              @click="defaultSize"
            />
          </div>
          <div class="controls-item" id="">
            <img
              src="../../assets/images/main/pdf.svg"
              alt="+"
              @click="openPDF"
            />
          </div>
          <div class="controls-item" id="">
            <img
              src="../../assets/images/main/excel.svg"
              alt="+"
              @click="downloadExcel"
            />
          </div>

          <!-- <div class="controls-item" id="">
            <img src="../../assets/images/main/width_screen.svg" alt="+" />
          </div>
          <div class="controls-item" id="">
            <img src="../../assets/images/main/hieght_screen.svg" alt="+" />
          </div>
          <div class="controls-item" id="reset" title="reset">
            <img src="../../assets/images/main/return.svg" alt="+" />
          </div>
          -->

          <form id="hotspot-form">
            <!--            <label style="margin-left: 7px;" id="status-message"-->
            <!--                    :style="{ color: isCapturingCoordinates ? 'green' : '#000' }">-->
            <!--              {{ statusMessage }}-->
            <!--            </label>-->

            <!--绑定快捷键 A+ S- -->
            <!--<el-button style="margin-left: 1px"
                type=""
                size="small"
            >
              Order
            </el-button>-->
            <el-input
              style="width: 45px; height: 17px; text-align: center"
              type="text"
              v-model="inputData.order"
              id="order"
              name="label"
            />
            <label for="des_Xincrement"></label>
            <el-input
              v-if="userInfo.userType == 1"
              style="width: 45px; height: 17px"
              type="text"
              v-model="inputData.Xincrement"
              id="des_Xincrement"
              name="Xincrement"
            />

            <label for="des_Yincrement"></label>
            <el-input
              v-if="userInfo.userType == 1"
              style="width: 45px; height: 17px"
              type="text"
              v-model="inputData.Yincrement"
              id="des_Yincrement"
              name="Yincrement"
            />

            <!--请保留：调试用于显示-->
            <!--<label for="des_pointLx">Lx:</label>
                  <el-input
                    style="width: 45px;height: 17px"
                    type="text"
                    v-model="inputData.pointLx"
                    id="des_pointLx"
                    name="pointLx"
                  />-->
            <!--请保留：调试用于显示-->
            <!-- <label for="des_pointLy">Ly:</label>
                  <el-input
                    style="width: 45px;height: 17px"
                    type="text"
                    v-model="inputData.pointLy"
                    id="des_pointLy"
                    name="pointLy"
                  />-->

            <el-button
              v-if="userInfo.userType == 1"
              style="margin-left: 1px"
              @click="coordinatesClick"
              type=""
              size="small"
            >
              Hot Area Preview
            </el-button>

            <el-button
              v-if="userInfo.userType == 1"
              @click="coordinatesConfirm"
              type="primary"
              style="margin-left: 12px"
              size="small"
              >Update</el-button
            >
            <el-button
              v-if="userInfo.userType == 1"
              @click="coordinatesDelete"
              type="danger"
              style="margin-left: 12px"
              size="small"
              >Delete</el-button
            >
            &nbsp;&nbsp;
            <span style="color: black"
              >'&nbsp;<span class="dot yellow-dot"></span>'
              <span style="text-decoration: underline">parts already in the cart;</span>
            </span>
            &nbsp;
            <span style="color: black"
              >'&nbsp;<span class="dot red-dot"></span>'
              <span style="text-decoration: underline">part changed;</span>
            </span>
            <!--<label
              style="margin-left: 7px"
              id="status-message"
              v-if="currentRow"
              :style="{ color: isCapturingCoordinates ? 'green' : '#000' }"
            >
              {{ statusMessage }} {{ currentRow.materielNameEn }} &lt;!&ndash;{{ currentRow.materielName }} &nbsp;&ndash;&gt;

            </label>-->
          </form>
        </div>

        <div
          id="canvas-container"
          ref="canvas_container"
          :style="{ height: canvasContainerHeight }"
        >

          <!-- currentRow非点击 仅仅显示 物料名称 和购物车一行       -->
          <div
            class="current-materiel-infor"
            v-if="currentRow"
            :style="{
              left: currentRow_showInfor_position.left + 'px',
              top: currentRow_showInfor_position.top + 'px',
              paddingBottom: isIconExpand ? '30px' : '5px',
            }"
          >
            <div
              style="
                display: flex;
                justify-content: space-between;
                background-color: #7a7281;  /*背景色current-materiel-infor*/
                height: 24px;
                color: #fff;
                align-items: center;
                padding-left: 143px; /*增加点击图标后  这里增加偏移值*/
              "
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ currentRow.materielNameEn }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </div>
            <div v-if="isIconExpand" style="padding: 0 8px">
              <div>PART NO.&nbsp;
                {{ currentRow.materielCode }}
              </div>
              <div>QTY.used：{{ currentRow.quantity }}&nbsp;&nbsp;&nbsp;
                <span  v-if="userInfo.userType == 1 && currentRow.warehouseAmount"
                       style="color:#f56c6c;text-decoration: underline">
                        Stock:&nbsp;{{ formatStock(currentRow.warehouseAmount) }}&nbsp;
                      <span v-if="currentRow.shelfCode">(&nbsp;{{ currentRow.shelfCode }}&nbsp;)</span>
                </span>
              </div>
              <div>Price/Unit：
                <!-- 动态显示币种符号 -->
                {{ userInfo.currency === '1' ? '¥' : userInfo.currency === '2' ? '$' : userInfo.currency === '3' ? '€' : '' }}
                {{ formatPrice(currentRow.price) }}
              </div>
              <div>Quantity：</div>
              <div>Index:{{ currentRow.indexNo }}</div>
            </div>
          </div>
          <!-- currentRow点击用 修改数量       -->
          <div
            v-if="currentRow && isIconExpand"
            :style="{
              position: 'absolute',
              zIndex: 3,
              left: currentRow_showInfor_position.left + 70+20 + 'px',
              top: currentRow_showInfor_position.top + 93 + 'px',
            }"
          >
            <el-input-number
              size="small"
              v-model="currentRow.amount"
              :min="1"
            />
          </div>
          <!-- currentRow点击用 上方隐藏等点击图标  物料信息点击图标        -->
          <div
            v-if="currentRow"
            :style="{
              // 背景色current-materiel-infor
              backgroundColor: '#7a7281',
              height: '24px',
              left: currentRow_showInfor_position.left + 'px',
              top: currentRow_showInfor_position.top + 'px',
              position: 'absolute',
              zIndex: 3,
              current: 'pointer',
              display: 'flex',
              alignItems: 'center',
            }"
          >

            <el-icon
              @click="isIconExpand = !isIconExpand"
              :style="{
                transform: isIconExpand ? 'rotate(90deg)' : 'rotate(0deg)',
                fontWeight: 'bold',
                marginRight: ' 3px',
                cursor: 'pointer',
                color: '#fff',
                fontSize: '20px',
              }"
              ><DArrowRight
            /></el-icon>
            <div style="height: 100%; width: 1px; background: #fff"></div>

            <el-button
                v-if="currentRow"
                link
                type="primary"
                @click.prevent="hideCurrentMateriel(currentRow)"
                title="hide"
            >
              <img
                  src="../../assets/images/main/deleteWite.svg"
                  alt="+"
                  height="18px"
              />
            </el-button>
            <div style="height: 100%; width: 1px; background: #fff"></div>

            <el-button
              v-if="currentRow"
              link
              type="primary"
              @click.prevent="addCart(currentRow)"
              title="Add a shopping cart"
            >
              <img
                src="../../assets/images/main/purCarWhite.svg"
                alt="+"
                height="18px"
              />
            </el-button>
            <div style="height: 100%; width: 1px; background: #fff"></div>

            <div
                class="copyable-code"
                @click="copyCode(currentRow.materielCode)"
            >
              <el-icon class="copy-icon"><DocumentCopy /></el-icon>
              <SPAN style="color:white;font-size: 13px;">P/N</SPAN>
            </div>
            <div style="height: 100%; width: 1px; background: #fff"></div>
          </div>
          <!-- currentRow点击用 下方购物车等点击图标   物料信息点击图标        -->
          <div
            class="current-materiel"
            v-if="currentRow && isIconExpand"
            :style="{
              left: currentRow_showInfor_position.left + 'px',
              top: currentRow_showInfor_position.top + 134 + 'px',
            }"
          >
            <div>
<!--              <img v-if="currentRow.existsCart == '1'"
                   src="../../assets/images/main/purCar.svg"
                   alt="+"
                   height="17px"
              />
              <img v-else
                   src="../../assets/images/main/purCarEmpty.svg"
                   alt="+"
                   height="17px"
              />-->
              <el-button
                size="small"
                type="danger"
                @click.prevent="addCart(currentRow)"
                title="Add a shopping cart"
              >
                      <span
                            v-if="currentRow.existsCart == '1'">
                                Remove
                              </span>
                      <span
                            v-else>
                                Add To Cart
                      </span>
              </el-button>
              <el-button
                link
                type="primary"
                @click.prevent="addFavorit(currentRow)"
                title="Add Favorites"
              >
                     <img v-if="currentRow.existsFavorites == '1'"
                           src="../../assets/images/main/favoritesRed.svg"
                          alt="+"
                          height="15px"
                    />
                    <img v-else
                         src="../../assets/images/main/favoritesRedEmpty.svg"
                         alt="+"
                         height="15px"
                    />
              </el-button>
              <el-button
                @click="showDialogChangeFn(currentRow)"
                link
                type="danger"
                size="small"
                style="
                  font-size: 14px;
                  font-weight: 600;
                  text-decoration: underline;
                "
              >
                TECH INFO.
              </el-button>
            </div>
          </div>
          <!-- 弹窗 Change List  -->
          <div
            class="changeList"
            :style="{
              position: 'fixed',
              zIndex: '100',
              left: `${dialogLeft}px`,
              width: '300px',
              backgroundColor: 'rgb(238, 238, 238)',
            }"
            v-if="showDialogChange"
          >
            <el-tabs v-model="activeTab_changeList" type="card">
              <el-tab-pane label="Fitted on" name="first">
                <template #label>
                  <img
                      src="../../assets/images/main/directory.svg"
                      alt="+"
                      height="20px"
                  />
                  <!--  <span class="toggle-icon">&raquo;</span>-->
                  &nbsp; Fitted on
                </template>
                <div class="content">
                    <!--  循环-->
                    <!-- current  currentUsedInData-->
                    <div v-for="(item, index) in currentUsedInData" class="current">
                      <div style="flex: 1; padding: 5px">
                        <div
                            style="
                            display: flex;
                            flex-direction: column;
                            padding: 5px;
                          "
                        >
                          <span style="text-decoration: underline"
                          >Fitted On Equipment:
                          </span>
                        </div>
                        <div>
                          {{item.materielNameEn}}
                        </div>
                        <div>
                          {{item.materielCode}}
                        </div>
                      </div>
                    </div>
                  <!-- current  -->
                  <div v-if="currentUsedInData.length==0" class="current">
                    <div style="flex: 1; padding: 5px">
                      <div
                          style="
                          display: flex;
                          flex-direction: column;
                          padding: 5px;
                        "
                      >
                        <span style="display: flex; align-items: center">
                          <img
                              src="../../assets/images/main/note.svg"
                              alt="+"
                              height="20px"
                          />Fitted On: &nbsp;<span class="exchange-false"
                        ><a href="">None!</a></span
                        >
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="Notice Of Change" name="second">
                <template #label>
                  <img
                    src="../../assets/images/main/change.svg"
                    alt="+"
                    height="17px"
                  />
                  <!--  <span class="toggle-icon">&raquo;</span>-->
                  &nbsp; Notice Of Change
                </template>
                <div class="content">
                  <!--  循环-->
                  <div v-for="(item, index) in currentNoticeData">
                    <!-- current  -->
                    <div  class="current">
                      <div style="flex: 1; padding: 5px">
                        <div
                          style="
                            display: flex;
                            flex-direction: column;
                            padding: 5px;
                          "
                        >
                          <div>
                            {{item.createDate}}
                          </div>
                          <span style="display: flex; align-items: center">
                            <img
                              src="../../assets/images/main/pdf.svg"
                              alt="+"
                              height="17px"
                            />
                                <span class="exchange-false">
                                  <a href="" @click="jumpFile(item)">{{item.files?.split("/").pop()}}</a>
                                </span>
                          </span>
                          <span style="text-decoration: underline">
                              Serial Number Range:{{item.serialNumberRange}}
                          </span>
                          <span style="text-decoration: underline">
                              {{item.description}}
                          </span>
                        </div>

                      </div>
                    </div>
                  </div>
                  <!-- current  -->
                  <div v-if="currentNoticeData.length==0" class="current">
                    <div style="flex: 1; padding: 5px">
                      <div
                        style="
                          display: flex;
                          flex-direction: column;
                          padding: 5px;
                        "
                      >
                        <span style="display: flex; align-items: center">
                          <img
                            src="../../assets/images/main/pdf.svg"
                            alt="+"
                            height="17px"
                          />Change Notice: &nbsp;<span class="exchange-false"
                            ><a href="">None!</a></span
                          >
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!--canvas 用于绘制图片 -->
          <canvas id="canvas" ref="canvas" style="z-index: 0"></canvas>
          <!-- overlayCanvas放置热点-->
          <canvas
            id="overlayCanvas"
            ref="overlayCanvas"
            style="z-index: 1"
          ></canvas>
          <!--绑定事件监听层 添加新热区预览小框框-->
          <canvas id="topCanvas" ref="topCanvas" style="z-index: 2"></canvas>
        </div>

        <!--  配件canvas页面下的--折叠表格-->
        <div
          class="table-wrap"
          id="table"
          :style="{ height: tableHeight, left: isExpand ? '326px' : '100%' }"
        >
          <div class="close" @click="onClose">
            <el-icon>
              <ArrowRightBold />
            </el-icon>
          </div>
          <div class="table-resizer" id="table-resizer"></div>
          <el-table
            @row-click="tableRowClick"
            class="customColor-table"
            :data="tableData"
            border
            :style="{ height: tableHeight }"
            highlight-current-row
            ref="tableRef"
          >
            <el-table-column prop="indexNo" label="Index" :width="80" />
            <el-table-column
              prop="materielCode"
              label="Part Number"
              :width="200"
            />
<!--            <el-table-column prop="materielName" label="Name_China" />-->
            <el-table-column prop="materielNameEn" label="Description" />
            <el-table-column prop="price" label="Price">
              <template #default="{ row }">
                <!-- 动态显示币种符号 -->
                {{ userInfo.currency === '1' ? '¥' : userInfo.currency === '2' ? '$' : userInfo.currency === '3' ? '€' : '' }}
                {{ formatPrice(row.price) }}
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="Quantity" />
            <el-table-column prop="remarks" label="Remarks" />
            <el-table-column label="Operations" :width="120">
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  size="small"
                  @click.prevent="addCart(scope.row)"
                >
                      <img v-if="scope.row.existsCart == '1'"
                            src="../../assets/images/main/purCar.svg"
                            alt="+"
                            height="15px"
                      />
                      <img v-else
                           src="../../assets/images/main/purCarEmpty.svg"
                           alt="+"
                           height="15px"
                      />
                </el-button>
                <el-button
                  link
                  type="primary"
                  size="small"
                  @click.prevent="addFavorit(scope.row)"
                >
                     <img v-if="scope.row.existsFavorites == '1'"
                           src="../../assets/images/main/favorites.svg"
                          alt="+"
                          height="15px"
                    />
                    <img v-else
                         src="../../assets/images/main/favoritesEmpty.svg"
                         alt="+"
                         height="15px"
                    />
                </el-button>
                <!--  <el-button
                  link
                  type="primary"
                  size="small"
                  @click.prevent="goBuy(scope.row)"
                >
                  <img
                    src="../../assets/images/main/buyIt.svg"
                    alt="+"
                    height="15px"
                  />
                </el-button>-->
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <!--  配件canvas页面下的--表格折叠 图标 图标 图标   -->
      <div
        v-show="!isExpand && Src.includes('.png') && Src.length !== 0"
        class="expand"
        @click="onExpand"
      >
        <el-icon>
          <ArrowLeftBold />
        </el-icon>
      </div>

      <!--  mp4页面   -->
      <video
        controls
        v-show="Src.includes('.mp4') && Src.length !== 0"
        style="width: 100%; height: auto"
        :src="Src"
      >
        <!-- <source type="video/mp4" /> -->
        您的浏览器不支持 video 标签。
      </video>
      <!--  product页面   -->
      <!--  product页面  方法名不同 -->
      <!--addWholeCart addWholeFavorit addWholeBuy -->
      <div
        style="display: flex; flex-direction: row"
        v-show="Src.includes('product') && Src.length !== 0"
      >
        <div style="padding: 0px 16px" v-if="text">
          <div
            v-if="tab_list.product[0]"
            style="background: #eee; padding: 16px; padding-left: 32px"
          >
            <div style="width: 500px; display: inline-block; text-align: right">
              <h2 style="flex: 1; margin-bottom: 5px">
                {{ tab_list.product[0].tittle }}
              </h2>

              <p style="margin-bottom: 5px">
                <!--                <span>{{ tab_list.product[0].materielName }}</span>
                <span> &nbsp;&nbsp;&nbsp;</span
                ><span>{{ tab_list.product[0].materielNameEn }}</span>
                <span> &nbsp;&nbsp;&nbsp;</span>-->
                <span>{{ tab_list.product[0].materielCode }}</span>
              </p>
              <div
                class="control-icon"
                style="color: darkgoldenrod; font-size: 22px"
              >
                <span>
                     <!-- 动态显示币种符号 -->
                    {{ userInfo.currency === '1' ? '¥' : userInfo.currency === '2' ? '$' : userInfo.currency === '3' ? '€' : '' }}
                  {{ formatPrice(tab_list.product[0].price) }}
                </span
                ><span> &nbsp;&nbsp;&nbsp;</span>
                <el-popover
                  :visible="buyPopoverVisible"
                  trigger="click"
                  width="160px"
                >
                  <template #reference>

                    <img  v-if="tab_list.product[0].existsCart == '1'"
                      @click="buyPopoverVisible = true"
                      src="../../assets/images/main/purCarYellow.svg"
                      alt="+"
                      style="height: 20px"
                    />
                    <img  v-else
                      @click="buyPopoverVisible = true"
                      src="../../assets/images/main/purCarYellowEmpty.svg"
                      alt="+"
                      style="height: 20px"
                    />

                  </template>
                  <template #default>
                    <!-- <div style="font-size: 12px;margin-bottom: 6px;">
                    Please enter the number of shopping
                  </div> -->
                    <el-input-number
                      v-model="buyNumber"
                      :min="1"
                      :max="9999"
                      size="small"
                      style="width: 100%"
                    />
                    <div style="margin-top: 10px; text-align: right">
                      <el-button size="small" @click="buyPopoverVisible = false"
                        >cancel</el-button
                      >
                      <el-button
                        @click.prevent="addWholeCart(tab_list.product[0])"
                        type="primary"
                        size="small"
                      >
                        confirm
                      </el-button>
                    </div>
                  </template>
                </el-popover>
                <!-- <img
                    @click.prevent="addWholeCart()"
                    src="../../assets/images/main/purCarRed.svg"
                    alt="+"
                    style="height:20px;"
                /> -->
                    <img   v-if="tab_list.product[0].existsFavorites == '1'"
                           @click.prevent="addWholeFavorit(tab_list.product[0])"
                           src="../../assets/images/main/favoritesYellow.svg"
                           alt="+"
                           style="height: 20px"
                    />
                    <img v-else  @click.prevent="addWholeFavorit(tab_list.product[0])"
                         src="../../assets/images/main/favoritesYellowEmpty.svg"
                         alt="+"
                         style="height: 20px"
                    />
                <!-- <img
                    @click.prevent="addWholeBuy()"
                    src="../../assets/images/main/buyItRed.svg"
                    alt="+"
                    style="height:20px;"
                />-->
              </div>
              <h4 style="margin: 8px 0">
<!--                {{ tab_list.product[0].description }}-->
              </h4>

              <img
                style="width: 450px; margin: 8px 0"
                :src="`${base_downFileByPath_url}${carousel_list?.[0]?.filePath}`"
              />
              <!--   细节图轮播效果   暂时取消      -->
              <!--<el-carousel
                  :interval="5000"
                  arrow="always"
                  type="card"
                  height="100px"
              >
                <el-carousel-item v-for="item in carousel_list_details">
                  <img
                      style="height: 100px;width: auto"
                      :src="`${base_downFileByPath_url}${item.filePath}`"
                  />
                </el-carousel-item>
              </el-carousel>-->
            </div>
            <img
              style="width: 500px; vertical-align: top; padding-left: 30px"
              :src="`${base_downFileByPath_url}${product_params_img.filePath}`"
            />
          </div>
        </div>
      </div>
      <!--  manual手册页面   -->
      <div v-show="Src.includes('manual') && Src.length !== 0">
        <div>
          <el-result icon="info" title="PDF Handbook">
            <template #sub-title>
              <p>You can preview online or download it</p>
            </template>
            <template #extra>
              <el-button type="danger" size="small" @click="loadPdf">
                Preview PDF online
              </el-button>
            </template>
          </el-result>
        </div>
        <canvas id="pdfCanvas" ref="pdfCanvas"></canvas>
      </div>
    </main>
  </div>
</template>

<script setup>
import {
  onMounted,
  onBeforeUnmount,
  ref,
  provide,
  watch,
  reactive,
  nextTick,
  inject,
} from "vue";
import {
  api_post_userInfoData,
  api_get_menu,
  api_get_table_data,
  api_get_coordinate,
  api_save_coordinate,
  api_get_product,
  api_delete_coordinate,
  api_post_addToFavorites,
  api_post_addToCart,
  api_get_catalog_byId,
  api_url_bom_items_excel,
  api_post_currentUserShopCarListPage,
  api_post_listData_currentUserShopCar, api_post_pmcNotice_listData, api_get_BomItems_usedIn,
  formatPrice,findNodeByField_tree
} from "../../utils/api";
import RecursiveMenu from "./components/RecursiveMenu.vue";
import { ElLoading, ElMessage } from "element-plus";
import {
  ArrowLeftBold,
  ArrowRightBold,
  DArrowRight,
} from "@element-plus/icons-vue";
import { DocumentCopy } from '@element-plus/icons-vue'
//useRoute 用于获取当前路由信息，而 useRouter 用于执行导航操作
import { useRoute, useRouter } from "vue-router";
import BreadCrumb from "../../components/BreadCrumb.vue";
import { useRouteParamsStore } from "../../stores/useRouteParamsStore.js";
//pdf
import { getDocument, GlobalWorkerOptions } from "pdfjs-dist/legacy/build/pdf";
import { useBreadcrumbStore } from "../../stores/useBreadcrumbStore.js";
import * as pdfjs from "pdfjs-dist";

// Set the worker URL
GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.mjs`;
console.log(GlobalWorkerOptions.workerSrc);

// ... your other code ...

const breadcrumbStore = useBreadcrumbStore();
const rpStore = useRouteParamsStore();
const base_img_url = inject("$base_img_url");
const base_downFileByPath_url = inject("$base_downFileByPath_url");
const currentItem = ref(null);

const text = ref(null);
//仅为了和 proudct页面里面产品页参数统一
const tab_list = reactive({
  boms: [],
  product: [],
  manual: [],
  video: [],
});
const menuData = ref([]);
//product 节点
const productNodeCatalog = ref();

const tableRef = ref();
const defaultOpeneds = ref([]);
const route = useRoute();
const router = useRouter();
const Src = ref("");
const tableData = ref([]);
const cartData = ref([]);
const noticeData = ref([]);
const currentNoticeData = ref([]);
const currentUsedInData = ref([]);
const tableHeight = ref("200px");
const tableKey = ref(0);
const isExpand = ref(false);
const isIconExpand = ref(true);
const currentRow = ref(null);
const currentRow_showInfor_position = ref({ left: 0, top: 0 });
// canvas
const canvasContainerHeight = ref(window.innerHeight - 160 + "px");
const canvas_container = ref(null);
const canvas = ref(null);
const overlayCanvas = ref(null);
const topCanvas = ref(null);
const ctx = ref(null);
const overlayCtx = ref(null);
const topCtx = ref(null);
const canvas_img = new Image();
const isCapturingCoordinates = ref(false);
const dragStart = ref(null);
const statusMessage = ref();
const current = ref();
// clickedRect
const clickedRectCurrent = ref(null);
const coordinates = ref([]);
const coordinatesLength = ref(0);
const coordinatesID = ref();

let scaleFactor;
const inputData = reactive({
  order: 1,
  pointLx: 0,
  pointLy: 0,
  pointRx: 0,
  pointRy: 0,
  Xincrement: 60,
  Yincrement: 60,
});
const translate = reactive({
  scale: 1,
  offsetX: 0,
  offsetY: 0,
});
const userInfo = reactive({});

//manual下的pdf手册
const pdfCanvas = ref();

const buyPopoverVisible = ref(false);
const buyNumber = ref(1);

//控制更改列表弹窗位置
//vue的检测值变化watch(showDialogChange
const activeTab_changeList = ref("first");
const showDialogChange = ref(false);
const dialogLeft = ref(0);
const canvasHeight = ref(0);
function setDialogPosition() {
  const canvas = document.getElementById("canvas");
  if (canvas) {
    const rect = canvas.getBoundingClientRect();
    dialogLeft.value = rect.left + rect.width - 300;
    canvasHeight.value = rect.height;
  }
}

function handleClick(canvasName, event) {
  let rect;
  if (canvasName === "canvas") {
    rect = canvas.value.getBoundingClientRect();
  } else if (canvasName === "overlayCanvas") {
    rect = overlayCanvas.value.getBoundingClientRect();
  } else if (canvasName === "topCanvas") {
    rect = topCanvas.value.getBoundingClientRect();
  }

  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;
  console.log(`Clicked on ${canvasName} at position: (${x}, ${y})`);
}

const copyCode = (text) => {
  if (!text) {
    ElMessage.warning('No Part Number To Copy')
    return
  }

  navigator.clipboard.writeText(text)
      .then(() => {
        ElMessage.success('Part Number Copied To Clipboard')
      })
      .catch(err => {
        console.error('Failed To Copy:', err)
        ElMessage.error('Failed To Copy Part Number')
      })
}

// 新增的库存数量格式化方法
const formatStock = (value) => {
  if (!value) return '0'
  const number = Number(value)
  // 判断小数部分是否为零
  return number % 1 === 0
      ? number.toFixed(0)  // 整数部分
      : number.toFixed(2).replace(/\.?0+$/, '') // 保留非零小数并去除末尾零
}

function removeFileExtension(filename) {
  const lastDotIndex = filename.lastIndexOf(".");
  if (lastDotIndex === -1) {
    // 没有找到点，表示没有扩展名
    return filename;
  }
  return filename.substring(0, lastDotIndex);
}

const getText = async (arr) => {
  const paths = arr.filter((item) => item.fileName.includes("txt"));
  const path = paths.length > 0 ? paths[0].filePath : "";
  const result = await fetch(`${base_downFileByPath_url}${path}`, {}).then(
    (res) => res.text()
  );
  text.value = result;
};

const scrollToRow = async (rowIndex) => {
  await nextTick(); // Ensure DOM is updated

  // Use requestAnimationFrame to ensure rendering is complete
  requestAnimationFrame(() => {
    const table = tableRef.value.$el;
    const bodyWrapper = table.querySelector(".el-table__body-wrapper");
    const rows = bodyWrapper.querySelectorAll(".el-table__row");

    if (rows[rowIndex]) {
      const row = rows[rowIndex];
      bodyWrapper.scrollTop =
        row.offsetTop - bodyWrapper.clientHeight / 2 + row.clientHeight / 2;
      tableRef.value.scrollTo(
        0,
        row.offsetTop - bodyWrapper.clientHeight / 2 + row.clientHeight / 2 + 10
      );
    }
  });
};
const setCurrent = (index) => {
  tableRef.value.setCurrentRow(tableData.value[index]);
  scrollToRow(index);
};
const carousel_list = ref([]);
const carousel_list_details = ref([]);
const product_params_img = ref();
const prevRect = ref();
const getProduct = async (item) => {
  const resp = await api_get_product({
    id: item.id,
  });
  currentItem.value = item;
  console.log("---item为当前点击的product---");
  text.value = item;

    // 复制product页面时参数 统一
  tab_list.product[0] = item;

  const products = resp.filter((item) => item.filePath.includes("product"));
  carousel_list.value = products
    .filter((item) => item.filePath.includes("/product/product"))
    .sort((a, b) => a.filePath.localeCompare(b.filePath));

  carousel_list_details.value = products.filter((item) =>
    item.filePath.includes("/product/detail")
  );
  console.log(carousel_list_details);

  product_params_img.value = products.filter((item) =>
    item.filePath.includes("parameter")
  )[0];

  //1购物车  2收藏夹
  const currentUserShopCar = await api_post_listData_currentUserShopCar()
  if (currentUserShopCar.some(cart1 => cart1.type == "1" && cart1.bomItemsId === item.id)) {
    item.existsCart="1";
  }
  if (currentUserShopCar.some(cart1 => cart1.type == "2" && cart1.bomItemsId === item.id)) {
    item.existsFavorites="1";
  }
};

const handleMenuItemClick = (item) => {

  let filepath = item.filePath;
  console.log("handleMenuItemClick------------------------");
  console.log("handleMenuItemClick:"+filepath);

  current.value = item;
  //debugger;

  if (
    filepath.includes(".pdf") &&
    !filepath.includes("/manual") &&
    !filepath.includes("/video")
  ) {

    // 找到product 节点
  /*  const lastSlashIndex = filepath.lastIndexOf('/');
    var product_obj = findNodeByField_tree(menuData.value,"filePath",filepath.substring(0, lastSlashIndex + 1) + 'product')
    productNodeCatalog.value = product_obj;
    console.log("productNodeCatalog----------------------");
    console.log(product_obj);*/

    let handle_path;
    handle_path = item.filePath.split("/").filter((item) => item.trim() !== "");
    //数组handle_path最后一个元素之前插入"boms"
    handle_path.splice(handle_path.length - 1, 0, "boms");
    handle_path = handle_path.join("/").replace("pdf", "png");
    filepath = handle_path;
    Src.value = `${base_downFileByPath_url}${filepath}`;
    //切换菜单，选中的热点信息展示清空
    currentRow.value = null;
    //清除当前选中的热区
    clickedRectCurrent.value = null;
    //清除显示的Change List
    showDialogChange.value = false;

    //发现图片和热点错位，代码必须放在 canvas_img.onload中
    //代码必须放在 canvas_img.onload中
    // drawImage();
    const loading = ElLoading.service({
      lock: true,
      text: "Draw Hot Spots...",
      background: "rgba(255, 255, 255, 0.7)",
    });

    setCanvasSize();

    ctx.value.clearRect(0, 0, canvas.value.width, canvas.value.height);
    console.log(`output->Src.value`, Src.value);
    canvas_img.src = Src.value;

    canvas_img.onload = function () {
                scaleFactor = Number(canvas.value.height / canvas_img.height);

                // const width = canvas.value.height  * translate.scale
                const width = Number(canvas_img.width * scaleFactor * translate.scale);
                const height = Number(canvas.value.height * translate.scale);
                console.log(`output->width,height`, width, height);
                translate.offsetX = Number((canvas.value.width - width) / 2);
                // 绘制图片到canvas
                // 参数分别是：图片对象，图片在canvas上的x坐标，图片在canvas上的y坐标，图片的宽度（可选，不设置或设置为0则使用图片原尺寸），图片的高度（可选，不设置或设置为0则使用图片原尺寸）
                ctx.value.drawImage(
                    canvas_img,
                    translate.offsetX,
                    translate.offsetY,
                    width,
                    height
                );

            //requestAnimationFrame--浏览器通常会在处理完当前帧的所有绘图命令后才会调用下一帧
            //requestAnimationFrame--浏览器通常会在处理完当前帧的所有绘图命令后才会调用下一帧
            requestAnimationFrame(() => {

                setTimeout(() => {

                  getTableData_cartData(item.id).then(() => {
                    //需要等待热点数据更新到
                    getCoordinates(item.id).then(() => {

                      getNotice_listData().then(() => {
                        //绘制热点
                        //代码必须放在 canvas_img.onload中
                        drawSquares();
                        loading.close();
                      });
                    });
                  });
                }, 200);

            });
      // --canvas_img.onload  结束
    };

    canvas_img.onerror = function () {
      console.error("Failed to load image from source:", Src.value);
      loading.close();
      ElMessage.error("The image may not be maintained!");
    };


    //用于面包屑跳转到 path: '/detail'
    //用于面包屑跳转到 path: '/detail'
    breadcrumbStore.breadcrumbs = breadcrumbStore.breadcrumbs.map((itemNew) => {
      if (itemNew.type === "toDetail") {
        itemNew.id = item.id;
        itemNew.fileName = removeFileExtension(item.fileName);
        itemNew.filePath = item.filePath;
      }
      return itemNew;
    });

    console.log("Menu item clicked:", item);
  } else if (filepath.includes("/product")) {
    getProduct(item);
  } else if (filepath.includes("/manual")) {
    // window.open("/fileviewer" + item.filePath, "_blank");
    Src.value = `${base_downFileByPath_url}${filepath}`;
  } else {
  }

  Src.value = `${base_downFileByPath_url}${filepath}`;
};

// 使用 provide 传递方法
provide("menuItemClick", handleMenuItemClick);

const removeBomsItems = (items) => {
  return items
    .filter((item) => !["boms"].includes(item.fileName))
    .map((item) => ({
      ...item,
      children: item.children ? removeBomsItems(item.children) : undefined,
    }));
};
function filterFiles(files, extensions) {
  return files.filter((item) => {
    if (
      item.fileName &&
      extensions.some((ext) => item.fileName.endsWith(ext))
    ) {
      return false; // Exclude this item if its name ends with one of the extensions
    }

    // Recursively filter children if they exist
    if (item.children && item.children.length > 0) {
      item.children = filterFiles(item.children, extensions);
    }

    return true; // Include item if it's not excluded
  });
}
function removeChildren(arr, fileName) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].fileName === fileName) {
      delete arr[i].children;
    }
    if (arr[i].children) {
      removeChildren(arr[i].children, fileName);
    }
  }
}



const setCanvasSize = () => {
  //debugger;
  ctx.value = canvas.value.getContext("2d");
  overlayCtx.value = overlayCanvas.value.getContext("2d");
  topCtx.value = topCanvas.value.getContext("2d");
  nextTick(() => {
    canvas.value.width = canvas_container.value.clientWidth;
    canvas.value.height = canvas_container.value.clientHeight;
    overlayCanvas.value.width = canvas_container.value.clientWidth;
    overlayCanvas.value.height = canvas_container.value.clientHeight;
    topCanvas.value.width = canvas_container.value.clientWidth;
    topCanvas.value.height = canvas_container.value.clientHeight;
  });
};

const drawImage = async () => {
  const loading = ElLoading.service({
    text: "loading...",
    background: "rgba(255, 255, 255, 0.7)",
  });
  setCanvasSize();

  ctx.value.clearRect(0, 0, canvas.value.width, canvas.value.height);
  console.log(`output->Src.value`, Src.value);
  canvas_img.src = Src.value;

  canvas_img.onload = function () {
    scaleFactor = Number(canvas.value.height / canvas_img.height);

    // const width = canvas.value.height  * translate.scale
    const width = Number(canvas_img.width * scaleFactor * translate.scale);
    const height = Number(canvas.value.height * translate.scale);
    console.log(`output->width,height`, width, height);
    translate.offsetX = Number((canvas.value.width - width) / 2);
    // 绘制图片到canvas
    // 参数分别是：图片对象，图片在canvas上的x坐标，图片在canvas上的y坐标，图片的宽度（可选，不设置或设置为0则使用图片原尺寸），图片的高度（可选，不设置或设置为0则使用图片原尺寸）
    ctx.value.drawImage(
      canvas_img,
      translate.offsetX,
      translate.offsetY,
      width,
      height
    );
    loading.close();
  };

  canvas_img.onerror = function () {
    console.error("Failed to load image from source:", Src.value);
    loading.close();
    ElMessage.error("The image may not be maintained!");
  };
};

const translateCanvas = () => {
  ctx.value.clearRect(0, 0, canvas.value.width, canvas.value.height);
  overlayCtx.value.clearRect(
    0,
    0,
    overlayCanvas.value.width,
    overlayCanvas.value.height
  );

  //topCtx.value.clearRect(0, 0, topCanvas.value.width, topCanvas.value.height);
  // ctx.value.save()

  const width = canvas_img.width * scaleFactor * translate.scale;
  const height = canvas.value.height * translate.scale;
  console.log(`output->width1,height1`, width, height);

  // 绘制图片到canvas
  // 参数分别是：图片对象，图片在canvas上的x坐标，图片在canvas上的y坐标，图片的宽度（可选，不设置或设置为0则使用图片原尺寸），图片的高度（可选，不设置或设置为0则使用图片原尺寸）
  ctx.value.drawImage(
    canvas_img,
    translate.offsetX,
    translate.offsetY,
    width,
    height
  );

  //ctx.value.restore()
  drawSquares();
  //设置显示的位置
  //选中的信息展示 跟随点击 变换位置
  setPositon_current_materiel_infor();
};
const setPositon_current_materiel_infor = () => {
  var offset = { left: 50, top: 50 };
  if (clickedRectCurrent.value) {
    var position = {
      left:
        clickedRectCurrent.value.pointLx * translate.scale * scaleFactor +
        translate.offsetX +
        offset.left,
      top:
        clickedRectCurrent.value.pointLy * translate.scale * scaleFactor +
        translate.offsetY +
        +offset.top,
    };
  } else {
    var position = {
      left: 0,
      top: 0,
    };
  }

  currentRow_showInfor_position.value = position;
};

const getTableData_cartData = async (id) => {
  const result = await api_get_table_data({ bomId: id });
  tableData.value = result;

  var produtList_cartList = await api_post_currentUserShopCarListPage();
  produtList_cartList.forEach((product_car) => {
    product_car.cartList.forEach((cart) => {
      cartData.value.push(cart);
    });
  });
};

const getNotice_listData = async () => {
  var notice_listData = await api_post_pmcNotice_listData({status:"2"});
  noticeData.value = notice_listData;
};

const getCartData = async () => {
  cartData.value =[];
  var produtList_cartList = await api_post_currentUserShopCarListPage();
  produtList_cartList.forEach((product_car) => {
    product_car.cartList.forEach((cart) => {
      cartData.value.push(cart);
    });
  });
};

const clearCanvas = () => {
  if (canvas.value) {
    ctx.value.clearRect(0, 0, canvas.value.width, canvas.value.height);
    drawImage();
    //绘制热点
    drawSquares();
  }
};

const getCoordinates = async (id) => {
  coordinates.value = [];
  coordinates.value = await api_get_coordinate({ bomId: id });
  coordinates.value.forEach((item) => {
    item.hasid = true;
  });
  console.log("------Coordinages-------------");
  console.log("------Coordinages-------------");
  console.log(coordinates.value);
  coordinatesLength.value = coordinates.value.length;
};

const drawSquares = async () => {
  overlayCtx.value.clearRect(
    0,
    0,
    overlayCanvas.value.width,
    overlayCanvas.value.height
  );

  //overlayCtx.value.save();
  /*操作顺序，缩放，然后是旋转，最后是平移*/
  /*缩放*/
  const squares = coordinates.value.map((item) => ({
    x: Number(item.pointLx * translate.scale * scaleFactor),
    y: Number(item.pointLy * translate.scale * scaleFactor),
    rx: Number(item.pointRx * translate.scale * scaleFactor),
    ry: Number(item.pointRy * translate.scale * scaleFactor),
    id: item.id,
  }));
  console.log(squares, "squares");
  /*平移*/
  squares.forEach((square) => {
    overlayCtx.value.fillStyle = "rgba(0,0,255,0.2)";
    overlayCtx.value.fillRect(
      Number(square.x + translate.offsetX),
      Number(square.y + translate.offsetY),
      Number(square.rx - square.x),
      Number(square.ry - square.y)
    );
  });

  /*如果有当前选中的*/
  //将选中的更改颜色
  if (clickedRectCurrent.value) {
    const clickedRect = clickedRectCurrent.value;
    //前面已有总的清除颜色，新绘制的热点蓝色+即将绘制的热点红色=叠色(因此再次清除)
    overlayCtx.value.clearRect(
      Number(
        clickedRect.pointLx * translate.scale * scaleFactor + translate.offsetX
      ),
      Number(
        clickedRect.pointLy * translate.scale * scaleFactor + translate.offsetY
      ),
      Number(
        clickedRect.pointRx * translate.scale * scaleFactor + translate.offsetX
      ) -
        Number(
          clickedRect.pointLx * translate.scale * scaleFactor +
            translate.offsetX
        ),
      Number(
        clickedRect.pointRy * translate.scale * scaleFactor + translate.offsetY
      ) -
        Number(
          clickedRect.pointLy * translate.scale * scaleFactor +
            translate.offsetY
        )
    );
    overlayCtx.value.fillStyle = "rgba(255,0,0,0.2)";
    overlayCtx.value.fillRect(
      Number(
        clickedRect.pointLx * translate.scale * scaleFactor + translate.offsetX
      ),
      Number(
        clickedRect.pointLy * translate.scale * scaleFactor + translate.offsetY
      ),
      Number(
        clickedRect.pointRx * translate.scale * scaleFactor + translate.offsetX
      ) -
        Number(
          clickedRect.pointLx * translate.scale * scaleFactor +
            translate.offsetX
        ),
      Number(
        clickedRect.pointRy * translate.scale * scaleFactor + translate.offsetY
      ) -
        Number(
          clickedRect.pointLy * translate.scale * scaleFactor +
            translate.offsetY
        )
    );
  }
  //=====购物车==标记=========================
  //找出已经加入购物车的tableData
  var tableCar = tableData.value.filter((table) => {
    return cartData.value.some((car) => table.id == car.bomItemsId);
  });
  console.log("cartData-------");
  console.log(tableCar);

  //找出加入购物车的坐标数组
  var coordiCar = coordinates.value.filter((c) => {
    return tableCar.some((tbCar) => c.indexNo == tbCar.indexNo);
  });
  /*操作顺序，缩放，然后是旋转，最后是平移*/
  /*缩放*/
  const squares_car = coordiCar.map((item) => ({
    x: Number(item.pointLx * translate.scale * scaleFactor),
    y: Number(item.pointLy * translate.scale * scaleFactor),
    rx: Number(item.pointRx * translate.scale * scaleFactor),
    ry: Number(item.pointRy * translate.scale * scaleFactor),
    id: item.id,
  }));
  /*平移*/
  squares_car.forEach((square_car) => {
    overlayCtx.value.fillStyle = "rgba(231, 154, 22, 1)";
    // 计算圆点的中心位置
    const centerX =
      Number(square_car.x + translate.offsetX) +
      Number(0.1 * (square_car.rx - square_car.x));
    const centerY =
      Number(square_car.y + translate.offsetY) +
      Number(0.1 * (square_car.ry - square_car.y));
    // 计算半径（圆点半径是矩形宽度的一半)
    const radius = Number(0.25 * 0.7 * (square_car.rx - square_car.x));
    // 开始路径
    overlayCtx.value.beginPath();
    // 绘制圆点
    overlayCtx.value.arc(centerX, centerY, radius, 0, Math.PI * 2);
    // 填充圆点
    overlayCtx.value.fill();
  });

  //=====产品变更通知==标记======================
  //找出notice的tableData
  var tableNotice = tableData.value.filter((table) => {

    return noticeData.value.some((notice) => {
      // 将 notice.materielCode 分割成数组
      const materielCodeArray = notice.materielCode.split(',');
      // 检查数组中是否包含 table.materielCode，并且 productId 是否匹配
      return materielCodeArray.includes(table.materielCode) && notice.productId == table.productId;
    });

  });

  //找出notice的坐标数组
  var coordiNotice = coordinates.value.filter((c) => {
    return tableNotice.some((tbNotice) => c.indexNo == tbNotice.indexNo);
  });

  /*操作顺序，缩放，然后是旋转，最后是平移*/
  /*缩放*/
  const squares_notice = coordiNotice.map((item) => ({
    x: Number(item.pointLx * translate.scale * scaleFactor),
    y: Number(item.pointLy * translate.scale * scaleFactor),
    rx: Number(item.pointRx * translate.scale * scaleFactor),
    ry: Number(item.pointRy * translate.scale * scaleFactor),
    id: item.id,
  }));
  /*平移*/
  squares_notice.forEach((square_notice) => {
    overlayCtx.value.fillStyle = "rgba(220, 20, 60, 1)";
    // 计算圆点的中心位置
    const centerX =
        Number(square_notice.x + translate.offsetX) +
        Number(0.1 * (square_notice.rx - square_notice.x));
    const centerY =
        Number(square_notice.y + translate.offsetY) +
        Number(0.5 * (square_notice.ry - square_notice.y));
    // 计算半径（圆点半径是矩形宽度的一半)
    const radius = Number(0.25 * 0.7 * (square_notice.rx - square_notice.x));
    // 开始路径
    overlayCtx.value.beginPath();
    // 绘制圆点
    overlayCtx.value.arc(centerX, centerY, radius, 0, Math.PI * 2);
    // 填充圆点
    overlayCtx.value.fill();
  });


};

const coordinatesConfirm = async () => {
  if (inputData.order === null) {
    ElMessage.error("请输入order值");
    return;
  }
  console.log(inputData);
  const result = await api_save_coordinate(
    JSON.stringify({
      bomId: current.value.id,
      id: inputData.id,
      indexNo: Number(inputData.order),
      pointLx: Number(inputData.pointLx),
      pointLy: Number(inputData.pointLy),
      pointRx: Number(inputData.pointRx),
      pointRy: Number(inputData.pointRy),
    })
  );
  if (result.code === 0) {
    inputData.order = parseInt(inputData.order) + 1;

    coordinates.value = [];
    coordinates.value = await api_get_coordinate({ bomId: current.value.id });
    coordinates.value.forEach((item) => {
      item.hasid = true;
    });
    console.log("---coordinatesConfirm---Coordinages-------------");
    console.log("---coordinatesConfirm---Coordinages-------------");
    console.log(coordinates.value);
    coordinatesLength.value = coordinates.value.length;
    drawSquares();
  } else {
    ElMessage.error("Failed!");
  }
};

const coordinatesCancel = () => {
  clearCanvas();
  isCapturingCoordinates.value = false;

  statusMessage.value = "";
};

const coordinatesDelete = async (id) => {
  if (!coordinatesID.value) {
    ElMessage.error("Please Select Hot Area After delete");
    return;
  }
  const result = await api_delete_coordinate({
    bomId: current.value.id,
    id: coordinatesID.value,
  });
  if (result.code === 0) {
    coordinates.value = [];
    coordinates.value = await api_get_coordinate({ bomId: current.value.id });
    coordinates.value.forEach((item) => {
      item.hasid = true;
    });
    console.log("--coordinatesDelete----Coordinages-------------");
    console.log("--coordinatesDelete----Coordinages-------------");
    console.log(coordinates.value);
    coordinatesLength.value = coordinates.value.length;
    clickedRectCurrent.value = null;
    drawSquares();
  }
};
const tableRowClick = (row, column, event) => {
  console.log("---tableRowClick---");
  var currentRowIndexNo = row.indexNo;
  console.log(currentRowIndexNo);
  //currentRow赋值
  currentRow.value = row;
  var cCoordinate = coordinates.value.find((item) => {
    return item.indexNo == currentRowIndexNo;
  });

  if (cCoordinate) {
    //先算出当前序号在图片上的实际坐标位置
    translate.scale = 1.5;
    var cLx = Number(
      cCoordinate.pointLx * translate.scale * scaleFactor + translate.offsetX
    );
    var cLy = Number(
      cCoordinate.pointLy * translate.scale * scaleFactor + translate.offsetY
    );
    //canvas的视觉中点(假设)
    var canvasMidpointX = 1500 * translate.scale * scaleFactor;
    var canvasMidpointY = 300 * translate.scale * scaleFactor;
    //加上偏移值
    translate.offsetX += canvasMidpointX - cLx;
    translate.offsetY += canvasMidpointY - cLy;
    clickedRectCurrent.value = cCoordinate;
    requestAnimationFrame(translateCanvas);
  }
};

const defaultSize = () => {
  translate.offsetY = 0;
  translate.scale = 1;
  const width = canvas_img.width * scaleFactor * translate.scale;
  translate.offsetX = (canvas.value.width - width) / 2;
  translateCanvas();
};

const openPDF = () => {
  // 使用encodeURIComponent处理空格等特殊字符
  var url = base_downFileByPath_url + current.value.filePath;
  const encodedUrl = encodeURIComponent(url);
  window.open(encodedUrl, "_blank");
};

const downloadExcel = () => {
  loadDown(api_url_bom_items_excel + current.value.id);
};

const wheelUp = () => {
  if (translate.scale > 10) return;
  translate.scale *= 1.1;
  requestAnimationFrame(translateCanvas);
};
const wheelDown = () => {
  if (translate.scale < 1) return;
  translate.scale /= 1.1;
  requestAnimationFrame(translateCanvas);
};

const topCanvasClick = (event) => {
  console.log("click:---topCanvas");
  // if (isCapturingCoordinates.value) {
  //用于任何 DOM 元素。这个函数返回一个元素的大小及其相对于视口的位置（相对于视口的左上角）
  const rect = canvas.value.getBoundingClientRect();
  //算出 以canvas左上角为0点，的实际坐标
  const x = event.clientX - rect.left; // x坐标
  const y = event.clientY - rect.top; // y坐标
  console.log("---x:" + x);
  console.log("---y:" + y);
  const clickedRect = coordinates.value.find(
    (rect) =>
      x >=
        Number(
          rect.pointLx * translate.scale * scaleFactor + translate.offsetX
        ) &&
      x <=
        Number(
          rect.pointRx * translate.scale * scaleFactor + translate.offsetX
        ) &&
      y >=
        Number(
          rect.pointLy * translate.scale * scaleFactor + translate.offsetY
        ) &&
      y <=
        Number(rect.pointRy * translate.scale * scaleFactor + translate.offsetY)
  );
  /*&& clickedRect.hasid*/
  if (clickedRect) {
    console.log(`Selected...`, clickedRect);
    console.log(`Selected---------`);
    console.log(`Selected----------------`);
    statusMessage.value = "...";
    coordinatesID.value = clickedRect.id;

    if (clickedRectCurrent.value) {
      //先清除上次更改颜色的区域
      overlayCtx.value.clearRect(
        Number(
          clickedRectCurrent.value.pointLx * translate.scale * scaleFactor +
            translate.offsetX
        ),
        Number(
          clickedRectCurrent.value.pointLy * translate.scale * scaleFactor +
            translate.offsetY
        ),
        Number(
          clickedRectCurrent.value.pointRx * translate.scale * scaleFactor +
            translate.offsetX
        ) -
          Number(
            clickedRectCurrent.value.pointLx * translate.scale * scaleFactor +
              translate.offsetX
          ),
        Number(
          clickedRectCurrent.value.pointRy * translate.scale * scaleFactor +
            translate.offsetY
        ) -
          Number(
            clickedRectCurrent.value.pointLy * translate.scale * scaleFactor +
              translate.offsetY
          )
      );
      //恢复为原始渲染颜色
      overlayCtx.value.fillStyle = "rgba(0,0,255,0.2)";
      overlayCtx.value.fillRect(
        Number(
          clickedRectCurrent.value.pointLx * translate.scale * scaleFactor +
            translate.offsetX
        ),
        Number(
          clickedRectCurrent.value.pointLy * translate.scale * scaleFactor +
            translate.offsetY
        ),
        Number(
          clickedRectCurrent.value.pointRx * translate.scale * scaleFactor +
            translate.offsetX
        ) -
          Number(
            clickedRectCurrent.value.pointLx * translate.scale * scaleFactor +
              translate.offsetX
          ),
        Number(
          clickedRectCurrent.value.pointRy * translate.scale * scaleFactor +
            translate.offsetY
        ) -
          Number(
            clickedRectCurrent.value.pointLy * translate.scale * scaleFactor +
              translate.offsetY
          )
      );
    }

    const rowIdx = tableData.value.findIndex(
      (item) => item.indexNo === clickedRect.indexNo
    );

    console.log(rowIdx);
    console.log(clickedRect, "123");
    clickedRectCurrent.value = clickedRect;
    //清除显示的Change List
    showDialogChange.value = false;

    setCurrent(rowIdx);
    //currentRow赋值
    currentRow.value = tableData.value[rowIdx];
    console.log(currentRow.value);

    //选中后 可以用来更新的点
    inputData.id = clickedRect.id;
    inputData.order = clickedRect.indexNo;
    inputData.pointLx = clickedRect.pointLx;
    inputData.pointLy = clickedRect.pointLy;
    inputData.pointRx = clickedRect.pointRx;
    inputData.pointRy = clickedRect.pointRy;

    //先清除要更改颜色的区域
    overlayCtx.value.clearRect(
      Number(
        clickedRect.pointLx * translate.scale * scaleFactor + translate.offsetX
      ),
      Number(
        clickedRect.pointLy * translate.scale * scaleFactor + translate.offsetY
      ),
      Number(
        clickedRect.pointRx * translate.scale * scaleFactor + translate.offsetX
      ) -
        Number(
          clickedRect.pointLx * translate.scale * scaleFactor +
            translate.offsetX
        ),
      Number(
        clickedRect.pointRy * translate.scale * scaleFactor + translate.offsetY
      ) -
        Number(
          clickedRect.pointLy * translate.scale * scaleFactor +
            translate.offsetY
        )
    );
    overlayCtx.value.fillStyle = "rgba(255,0,0,0.2)";
    overlayCtx.value.fillRect(
      Number(
        clickedRect.pointLx * translate.scale * scaleFactor + translate.offsetX
      ),
      Number(
        clickedRect.pointLy * translate.scale * scaleFactor + translate.offsetY
      ),
      Number(
        clickedRect.pointRx * translate.scale * scaleFactor + translate.offsetX
      ) -
        Number(
          clickedRect.pointLx * translate.scale * scaleFactor +
            translate.offsetX
        ),
      Number(
        clickedRect.pointRy * translate.scale * scaleFactor + translate.offsetY
      ) -
        Number(
          clickedRect.pointLy * translate.scale * scaleFactor +
            translate.offsetY
        )
    );

    //设置显示的位置
    //选中的信息展示 跟随点击 变换位置
    var offset = { left: 50, top: 50 };
    var position = {
      left:
        clickedRectCurrent.value.pointLx * translate.scale * scaleFactor +
        translate.offsetX +
        offset.left,
      top:
        clickedRectCurrent.value.pointLy * translate.scale * scaleFactor +
        translate.offsetY +
        +offset.top,
    };

    //选中的信息展示 跟随点击 变换位置
    setPositon_current_materiel_infor();
  } else {
    // 检查是否同时按下了Ctrl/ctrl键
    if (event.ctrlKey) {
      // Ctrl/ctrl键被按下，并且鼠标也被点击了
      console.log("Ctrl/ctrl键+鼠标单击被触发了！");
      const rect = canvas.value.getBoundingClientRect();
      //算出 以canvas左上角为0点，的实际坐标
      const x = event.clientX - rect.left; // x坐标
      const y = event.clientY - rect.top; // y坐标

      //逆运算求图片上的坐标
      inputData.pointLx = parseInt(
        (x - translate.offsetX) / (translate.scale * scaleFactor)
      );
      inputData.pointLy = parseInt(
        (y - translate.offsetY) / (translate.scale * scaleFactor)
      );
      inputData.pointRx =
        Number(inputData.pointLx) + Number(inputData.Xincrement);
      inputData.pointRy =
        Number(inputData.pointLy) + Number(inputData.Yincrement);

      if (isCapturingCoordinates.value) {
        //新热区预览小框框显示的状态下才能保存
        inputData.id = null;
        coordinatesConfirm();
      }
    }
  }
};

const shortcutFunc = (event) => {
  console.log("shortcutFunc");
  if (isCapturingCoordinates.value) {
    //新热区预览小框框显示的状态下
    if (event.key === "a" || event.key === "A") {
      // A 键被按下时的处理
      console.log("A key is pressed.");
      inputData.order = parseInt(inputData.order) + 1;
    }
    if (event.key === "s" || event.key === "S") {
      // S 键被按下时的处理
      console.log("S key is pressed.");
      inputData.order = parseInt(inputData.order) - 1;
    }
  }
};

let hoverRectCache = { x: null, y: null, width: 0, height: 0 };
const previewHotArea = (e) =>{
  //添加新热区预览小框框
  //框框：添加新热区预览小框框
  if (isCapturingCoordinates.value) {
    const rect = topCanvas.value.getBoundingClientRect();

    const x = e.clientX - rect.left; // x坐标
    const y = e.clientY - rect.top; // y坐标

    // 清除之前的 hoverRect
    // 清除之前的 hoverRect
    if (hoverRectCache.x !== null) {
      topCtx.value.clearRect(0, 0, topCanvas.value.width,topCanvas.value.height);
    }

    // 绘制新的 hoverRect
    topCtx.value.strokeStyle = "rgba(0,0,255,0.3)"; // 设置边框颜色
    topCtx.value.lineWidth = 1; // 设置边框宽度

    topCtx.value.strokeRect(
        x,
        y,
        inputData.Xincrement * translate.scale * scaleFactor,
        inputData.Yincrement * translate.scale * scaleFactor
    );

    // 更新 hoverRect 信息
    hoverRectCache = {
      x: x,
      y: y,
    };
  }
}

const bindEvent = () => {
  let hoverRect = null;

  //鼠标单击的同事，按住ctrl键 添加新热点
  topCanvas.value.addEventListener("click", topCanvasClick);

  topCanvas.value.addEventListener("wheel", function (e) {
    e.preventDefault();

    if (e.deltaY < 0) {
      if (translate.scale > 10) return;
      translate.scale *= 1.1;
    } else {
      if (translate.scale < 1) return;
      translate.scale /= 1.1;
    }
    requestAnimationFrame(translateCanvas);
  });
  topCanvas.value.addEventListener("mousedown", function (e) {
    console.log("mousedown:---topCanvas");
    dragStart.value = { x: e.clientX, y: e.clientY };
  });

  topCanvas.value.addEventListener("mouseup", function (e) {
    dragStart.value = null;
  });

  topCanvas.value.addEventListener("mousemove", function (e) {
    if (dragStart.value) {
      const dx = e.clientX - dragStart.value.x;
      const dy = e.clientY - dragStart.value.y;
      translate.offsetX += dx;
      translate.offsetY += dy;
      dragStart.value = { x: e.clientX, y: e.clientY };
      requestAnimationFrame(translateCanvas);
    } else {

      //添加新热区预览小框框
      //框框：添加新热区预览小框框
      requestAnimationFrame(() => previewHotArea(e));

    }
  });

};

const coordinatesClick = () => {
  isCapturingCoordinates.value = !isCapturingCoordinates.value;
  if (isCapturingCoordinates.value) {
    statusMessage.value = "Positioning...";
  }
};

const init = () => {
  // setCanvasSize()
  // drawImage()
  bindEvent();
};

let startY, startHeight;
function initResizeTable(e) {
  const tableWrap = document.querySelector(".table-wrap");
  startHeight = tableWrap.offsetHeight;
  startY = e.clientY;
  console.log("startY",startY);
  document.addEventListener("mousemove", resizeTable);
  document.addEventListener("mouseup", stopResizeTable);
}

const resizeTable = (e) => {
  console.log("---resizeTable---");
  console.log("resiz--startY",startY);
  console.log("resiz--e.clientY",e.clientY);

  const deltaY = startY - e.clientY; // 计算鼠标移动的距离
  let newHeight = startHeight + deltaY; // 计算新的高度

// 限制高度在200px到700px之间
  if (newHeight > 700) {
    newHeight = 700; // 如果大于600，则设置为600
  } else if (newHeight < 200) {
    newHeight = 200; // 如果小于200，则设置为200
  } else {
  // 更新startHeight以便下一次拖动时基于当前高度
    startHeight = newHeight;
    startY = e.clientY; // 更新startY以便下一次拖动时基于当前鼠标位置
  }
  tableHeight.value = newHeight+"px";
  console.log("resiz--tableHeight",tableHeight.value);

};

const stopResizeTable = () => {
  document.removeEventListener("mousemove", resizeTable);
  document.removeEventListener("mouseup", stopResizeTable);
};

onMounted(async () => {
  window.document.querySelector(".header").classList.add("fixed");
  //product页面中toDetail方法设置 query: {params: item.filePath,}
  if (!route.query.params) {
    router.go(-1);
    return;
  }
  const { params } = route.query;
  //树菜单defaultOpeneds
  defaultOpeneds.value.push(params);
  const loading = ElLoading.service({
    text: "loading...",
    background: "rgba(255, 255, 255, 0.7)",
  });

  api_post_userInfoData().then((data) => {
    // 更新 userInfo 对象的属性，而不是重新赋值 userInfo 变量
    for (const key in data) {
      userInfo[key] = data[key];
    }
  });
  const menu = await api_get_menu();
  menuData.value = await removeBomsItems(menu);
  filterFiles(menuData.value, [".txt", ".xlsx", ".xls"]);
  removeChildren(menuData.value, "product");

  //绑定canvas鼠标事件
  init();
  loading.close();

  //设置添加热点时改变 order 字段的快捷键
  console.log("keydown-shortcutFunc");
  document.addEventListener("keydown", shortcutFunc);
  //设置table的拖动
  const tableResizer = document.getElementById("table-resizer");
  tableResizer.addEventListener("mousedown", initResizeTable);
});

watch(showDialogChange, (newValue) => {
  if (newValue) {
    // Update position when dialog is shown
    setDialogPosition();
  }
});

onBeforeUnmount(() => {
  const tableResizer = document.getElementById("table-resizer");
  tableResizer.removeEventListener("mousedown", initResizeTable);
  document.removeEventListener("keydown", shortcutFunc);
  window.document.querySelector(".header").classList.remove("fixed");
});

const onExpand = () => {
  isExpand.value = true;
};

const onClose = () => {
  isExpand.value = false;
};

const showDialogChangeFn = (currentRow) => {
  //显示Change List
  showDialogChange.value = !showDialogChange.value;
  console.log(currentRow);
  currentNoticeData.value = noticeData.value.filter(e=>e.materielCode.includes(currentRow.materielCode));
  //currentUsedInData
  api_get_BomItems_usedIn({materielCode:currentRow.materielCode}).then((data) => {
    currentUsedInData.value = data;
  });

};

const jumpFile = (item) => {
  var filePath = item.files;
  if (filePath) {
    var currentDomain = window.location.origin;
    // 拼接完整的链接
    var fullUrl = currentDomain + "/fileviewer/" + filePath;

    // 在新标签页中跳转到拼接后的链接
    window.open(fullUrl);
  }
}

//manual下的pdf手册
const loadPdf = async () => {
  const url = Src.value.replace(base_downFileByPath_url, "");
  window.open("/fileviewer" + url, "_blank");
};
//----------------------------------------
const hideCurrentMateriel = (row) => {
  currentRow.value=null;
};

const addCart = (row) => {
  api_post_addToCart([
    {
      amount: row.amount,
      bomItemsId: row.id,
      productType: "part",
    },
  ]).then((res) => {

    //实时更新cart数据，黄点才能正常显示
    getCartData();

    if ("0"==res.code) {
      row.existsCart="1";
     // row.existsFavorites="1";
      ElMessage.success({
        message: res.msg,
        duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
        offset: 150,
      });
    }else if(("1"==res.code) ){
      row.existsCart="0";
     // row.existsFavorites="0";
      ElMessage.error({
        message: res.msg,
        duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
        offset: 150,
      });
    }else{
      ElMessage.error({
        message: res.msg,
        duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
        offset: 150,
      });
    }

  });
};

const addWholeCart = (product) => {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(255, 255, 255, 0.7)",
  });
  api_post_addToCart([
    {
      amount: buyNumber.value,
      bomItemsId: currentItem.value.id,
      productType: "product",
    },
  ])
    .then((res) => {

      if ("0"==res.code) {
        product.existsCart="1";
       // product.existsFavorites="1";
        ElMessage.success({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else if(("1"==res.code) ){
        product.existsCart="0";
       // product.existsFavorites="0";
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else{
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }

      buyPopoverVisible.value = false;

    })
    .finally(() => {
      loading.close();
    });
};

const addFavorit = (row) => {
  getCartData();
  api_post_addToFavorites([
    {
      bomItemsId: row.id,
      productType: "part",
    },
  ]).then((res) => {

    if ("0"==res.code) {
      //row.existsCart="1";
      row.existsFavorites="1";
      ElMessage.success({
        message: res.msg,
        duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
        offset: 150,
      });
    }else if(("1"==res.code) ){
      //row.existsCart="0";
      row.existsFavorites="0";
      ElMessage.error({
        message: res.msg,
        duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
        offset: 150,
      });
    }else{
      ElMessage.error({
        message: res.msg,
        duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
        offset: 150,
      });

    }


  });
};
const addWholeFavorit = (product) => {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(255, 255, 255, 0.7)",
  });
  api_post_addToFavorites([
    {
      bomItemsId: currentItem.value.id,
      productType: "product",
    },
  ])
    .then((res) => {

      if ("0"==res.code) {
        //product.existsCart="1";
        product.existsFavorites="1";
        ElMessage.success({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else if(("1"==res.code) ){
        //product.existsCart="0";
        product.existsFavorites="0";
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else{
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }


    })
    .finally(() => {
      loading.close();
    });
};
const addWholeBuy = () => {
  const selectedList = [
    {
      ...currentItem.value,
      productFileList: [
        ...carousel_list.value,
        ...carousel_list_details.value,
      ].map((item) => item.filePath),
      cartList: [
        {
          ...currentItem.value,
          quantity: currentItem.value.amount,
          amount: currentItem.value.amount,
          id: currentItem.value.id,
          productType: "product",
        },
      ],
    },
  ];
  console.log(selectedList);
  rpStore.setParam("selectedList", selectedList);
  router.push("/createOrder");
};
const goBuy = async (item) => {
  const parts = current.value.filePath.split("/");
  parts.pop();
  parts.push("product");
  var product_file_path = parts.join("/");
  const [data] = await api_get_catalog_byId({
    filePath: product_file_path,
  });

  const selectedList = [
    {
      ...data,
      cartList: [
        {
          ...item,
          quantity: item.amount,
          amount: item.amount,
          id: item.id,
          productType: "product",
        },
      ],
    },
  ];
  console.log(selectedList);
  rpStore.setParam("selectedList", selectedList);
  router.push("/createOrder");
};

//-------导出excel公用方法--------------
function loadDown(url) {
  const loading = ElLoading.service({
    text: "loading...",
    background: "rgba(255, 255, 255, 0.7)",
  });
  var xhr = new XMLHttpRequest();
  xhr.open("POST", url, true); // 也可以使用POST方式，根据接口
  xhr.responseType = "blob";
  xhr.onload = function () {
    if (this.status === 200) {
      var blob = this.response;
      var reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onload = function (e) {
        loading.close();
        var headerName = xhr.getResponseHeader("Content-disposition");
        var fileName = decodeURIComponent(headerName).substring(20);
        //如果是IE，使用msSaveOrOpenBlob
        if (window.navigator.msSaveOrOpenBlob) {
          navigator.msSaveOrOpenBlob(blob, fileName);
        } else {
          var a = document.createElement("a");
          a.download = fileName;
          a.href = e.target.result;
          // 修复firefox中无法触发click
          document.body.appendChild(a);
          a.click();
          // 移除添加的元素
          document.body.removeChild(a);
        }
      };
    } else {
      loading.close();
      ElMessage.error("Erro!");
    }
  };
  var formdata = new FormData();
  xhr.send(formdata);
}
</script>

<style lang="less" scoped>
::v-deep {
  .el-table {
    .el-table__row.current-row {
      background-color: red !important;
      /* 这里设置你想要的颜色 */
    }
  }
}

::v-deep {
  .el-table__row.row-selected {
    .el-table__row.current-row {
      background-color: red;
      /* 你想要的颜色 */
    }
  }
}

 /* 通用圆点样式 */
    .dot {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 5px;
    }

    /* 黄色圆点 */
    .yellow-dot {
      background-color: rgba(231, 154, 22, 1);
    }

    /* 红色圆点 */
    .red-dot {
      background-color: crimson;
    }

.table-wrap {
  position: fixed;
  bottom: 0;
  left: 300px;
  right: 0;
  z-index: 100;
  overflow: hidden;
  transition: all 0.3s;
}

.expand {
  position: absolute;
  right: 38px;
  padding: 10px 3px;
  background: rgba(0, 0, 0, 0.3);
  text-align: center;
  font-size: 20px;
  bottom: 100px;
  color: #fff;
  cursor: pointer;
  z-index: 100;
  display: flex;
}

.close {
  display: flex;
  position: absolute;
  left: 0px;
  padding: 10px 3px;
  background: rgba(0, 0, 0, 0.3);
  text-align: center;
  font-size: 20px;
  bottom: 100px;
  color: #fff;
  cursor: pointer;
  z-index: 100;
}

.current-materiel-infor {
  position: absolute;
  background: #c6e2ff;//#ffdead//背景色current-materiel-infor
  color: black;
  z-index: 2; //和事件绑定的canvas必须一致
  font-size: 15px;
  padding-bottom: 30px;
  min-width: 195px;

  .materiel-label {
    width: 84px;
    display: inline-block;
    text-align: left;
  }
}

.current-materiel {
  position: absolute;
  padding: 3px;
  // background: #f56c6c;
  color: black;
  z-index: 3; //比事件绑定的canvas增加1个层级，用于点击
  font-size: 15px;

  .materiel-label {
    width: 84px;
    display: inline-block;
    text-align: left;
  }
}

.control-icon {
  > img {
    width: 28px;
    height: auto;
    margin-right: 16px;
    cursor: pointer;
  }
}

::v-deep {
  .fixed {
    position: fixed;
    width: 100%;
    top: 0;
  }

  .el-tabs__item {
    // background-color: #e8e8e8;
  }

  .el-carousel,
  .el-carousel__container {
    height: 400px !important;
  }
}

//changeList 样式
// changeList 样式
::v-deep {
  .el-tabs__nav {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
  .el-tabs__item {
    flex: 1;
    text-align: center;
    background-color: white; /* Change to your desired color */
  }
  .el-tabs__item.is-active {
    position: relative;
  }

  .el-tabs__item.is-active::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60%; /* Adjust width as needed */
    border-bottom: 2px solid green;
  }
}

.changeList {
  width: 100%;
  border: 1px solid #ccc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .toggle-icon {
    cursor: pointer;
    margin-right: 10px;
  }
  .content {
    padding: 2px;
  }
  .section-title {
    display: flex;
    align-items: center; /* 这将使得容器内的项目在竖直方向上居中 */
    font-weight: bold;
    font-size: 12px;
    margin-bottom: 2px;
  }
  .current,
  .history {
    background: #f8f9fb;
    border: 1px solid #ccc;
    margin-bottom: 10px;
  }
  .exchange-true {
    color: green;
  }
  .exchange-false {
    color: red;
  }
}

.copyable-code {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;

  .copy-icon {
    font-size: 14px;
    color: white;
    opacity: 1;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
    }
  }
}
</style>
