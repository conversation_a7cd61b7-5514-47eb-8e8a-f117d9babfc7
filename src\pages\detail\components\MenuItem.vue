<template>
  <el-sub-menu
    v-if="item.children && item.children.length"
    :index="removeBoms(item.filePath)"
  >
    <template #title>
      <!-- <el-icon><location /></el-icon> -->
      <img
        src="../../../assets/images/sideBar/folder.svg"
        style="width: 20px; height: 20px;"
      />
      <span>{{ removeFileExtension(item.fileName) }}</span>
    </template>
    <menu-item v-for="child in item.children" :key="child.id" :item="child" />
  </el-sub-menu>
  <el-menu-item
    v-else
    :index="removeBoms(item.filePath)"
    @click="handleClick(item)"
  >
    <!-- <el-icon><document /></el-icon> -->
    <img
      src="../../../assets/images/sideBar/file.svg"
      style="width: 20px; height: 20px;"
    />
    <span>{{  removeFileExtension(item.fileName) }}</span>
  </el-menu-item>
</template>

<script setup>
import { defineProps, inject } from 'vue'

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
})
function removeFileExtension(filename) {
    return filename.replace(/\.[^/.]+$/, "");
}

function removeBoms(str) {
  if (str.includes('/boms')) {
    return str.replace('/boms', '')
  } else {
    return str
  }
}

const menuItemClick = inject('menuItemClick')

const handleClick = (item) => {
  if (menuItemClick) {
    menuItemClick(item)
  }
}
</script>

<style lang="less" scoped>
::v-deep {
  //   .el-sub-menu__title {
  //     padding: 0 8px !important;
  //   }
}
</style>
