<template>
  <BreadCrumb :list="isFavor
    ? [
      {
        url: '/favor',
        name: '<PERSON>avor',
      },
    ]
    : [
      {
        url: '/cart',
        name: '<PERSON><PERSON>',
      },
    ]
    " />

  <div class="cart-content" v-loading="loading">
    <div class="cart-control">
      <el-checkbox style="margin-right: 15px" v-model="checkAll" @change="handleCheckAllChange">
        Check All
      </el-checkbox>
      <el-button v-if="isFavor" type="warning" text bg>
        For Ordering Repeatedly
      </el-button>
      <el-button @click="deleteSelected">Delete</el-button>

      <el-button type="danger" @click="onCreate">Generate New Order</el-button>
    </div>

    <div class="cart-list">
      <div class="shopping-cart-item-all head-item">
        <div style="display: flex; align-items: center">
          <div style="min-width: 170px; max-width: 170px; padding: 0 10px">
            Equipment/Fitted On
          </div>
          <div class="item-all-content" style="flex: 1">
            <div class="cart-all-item">
              <div class="item-all-checkbox" style="width: 14px"></div>
              <div class="cart-all-item-view index">Index</div>
              <div class="cart-all-item-info" style="padding: 0">
                Description
                <SortIcon @sort="onSort($event, 'materielNameEn')" sortKey="materielNameEn"
                  :currentKey="currentSortKey" />
              </div>

              <div class="cart-all-item-view materierCode">
                Part Number
                <SortIcon @sort="onSort($event, 'materielCode')" sortKey="materielCode" :currentKey="currentSortKey" />
              </div>
          <!-- <div class="cart-all-item-view quantity">Quantity</div>-->
              <div class="cart-all-item-view unitPrice" style="color: #213547; font-size: 14px; font-weight: normal">
                Price/Unit
              </div>
              <div
                  class="cart-all-item-view amount"
                  style="
                        text-align: center;
                        width: 100%; /* 新增 */

                    "
                  v-if="!isFavor"
              >
                Quantity
              </div>

              <div v-if="!isFavor" class="cart-all-item-view unitPrice"
                style="color: #213547; font-size: 14px; font-weight: normal">
                Total
              </div>
              <div class="cart-all-item-view materierCode">Components</div>
              <div class="cart-all-item-view operate"></div>

            </div>
          </div>
        </div>
      </div>
      <div class="shopping-cart-item-all" v-for="(commodityItem, itemIndex) in commodityList">
        <div class="item-all-name">
          <div class="item-all-image" style="width: 186px; padding-left: 10px">
            <!-- From The Machine -->
          </div>
          <el-checkbox class="item-all-checkbox" v-model="commodityItem.checked"
            @change="handleCheckItemAllChange($event, commodityItem)">
          </el-checkbox>

          {{ commodityItem.materielCode }}
          <!-- &nbsp;&nbsp;&nbsp; {{ commodityItem.materielName }}-->
          &nbsp;&nbsp;&nbsp; {{ commodityItem.materielNameEn }}
        </div>
        <div style="display: flex; align-items: flex-start">
          <div style="min-width: 170px; max-width: 170px; padding: 0 10px">
            <el-image class="wb-image" style="width: 100%; height: auto"
              :src="getImageUrl(commodityItem.productFileList || [])[0] || ''" :zoom-rate="1.2" :max-scale="7"
              :min-scale="0.2" :preview-src-list="getImageUrl(commodityItem.productFileList || [], 'all')
                " :initial-index="4" fit="cover" />
          </div>
          <div class="item-all-content" style="flex: 1">
            <div class="cart-all-item" v-for="(item, index) in commodityItem.cartList || []">
              <el-checkbox class="item-all-checkbox" @change="handleCheckItemChange($event, commodityItem)"
                v-model="item.checked">
              </el-checkbox>
              <div class="cart-all-item-view index">
                {{ getTableIndex(itemIndex, index) }}
              </div>
              <div class="cart-all-item-info">
                <!-- <img
                  src="../../assets/images/peijian.png"
                  alt=""
                  class="cart-all-item-img"
                /> -->
                <div class="cart-all-item-view materierCode">
                  <!--   {{ item.materielName }}&nbsp;&nbsp;&nbsp;-->
                  <el-button v-show="item.bomId && item.filePath && item.indexNo" @click="jumpDetail(item)" link
                             type="primary" size="small" style="font-size: 12px; font-weight: 600;text-decoration: underline;">
                    <img src="../../assets/images/main/find.svg" alt="+" height="17px" />
                    <!--                  Expl.View-->
                  </el-button>

                  {{ item.materielNameEn }}

                </div>
              </div>
              <div class="cart-all-item-view materierCode">
                {{ item.materielCode }}
              </div>

        <!--  <div class="cart-all-item-view quantity">
                {{ item.quantity }}
              </div>-->

              <div class="cart-all-item-view unitPrice">
                <!-- 动态显示币种符号 -->
                {{ userInfo.currency === '1' ? '¥' : userInfo.currency === '2' ? '$' : userInfo.currency === '3' ? '€' :
                  '' }}
                {{ formatPrice(item.unitPrice) }}
              </div>

              <div class="cart-all-item-view amount" v-if="!isFavor">
                <el-input-number v-model="item.amount" :min="item.quantity || 1" @change="amountChange(item)" />
              </div>

              <div class="cart-all-item-view unitPrice" v-if="!isFavor">
                <!-- 动态显示币种符号 -->
                {{ userInfo.currency === '1' ? '¥' : userInfo.currency === '2' ? '$' : userInfo.currency === '3' ? '€' :
                  '' }}
                {{ formatPrice(item.amountOfMoney) }}
              </div>
              <div class="cart-all-item-view materierCode">
                {{ item.fileName }}
              </div>

              <div class="cart-all-item-view operate"
                style="display: flex; align-items: center; justify-content:flex-start;">
                <div style="width: 28px;"  v-show="isFavor">
                  <el-button  link type="primary" size="small" @click.prevent="addCart(item)">
                    <img v-if="item.existsFavorites == '1'" src="../../assets/images/main/purCar.svg" alt="+"
                      height="17px" />
                    <img v-else src="../../assets/images/main/purCarEmpty.svg" alt="+" height="17px" />
                  </el-button>
                </div>
                <div style="width: 28px;" v-show="!isFavor">
                  <el-button  link type="primary" size="small" @click.prevent="addFavorit(item)">
                    <img v-if="item.existsFavorites == '1'" src="../../assets/images/main/favorites.svg" alt="+"
                      height="16px" />
                    <img v-else src="../../assets/images/main/favoritesEmpty.svg" alt="+" height="16px" />
                  </el-button>
                </div>

                <div>
                  <el-popover :visible="usedInVisible[item.materielCode]" trigger="click" width="160px">
                    <template #reference>

                      <el-button @click="usedInFunc(item.materielCode)" link type="primary" size="small"
                        style="font-size: 12px; font-weight: 600;text-decoration: none;">
                        Fitted on
                      </el-button>

                    </template>
                    <template #default>
                      <!--      冒泡显示内容              -->
                      <div v-for="(item, index) in currentUsedInData[item.materielCode]" class="current">
                        <div style="flex: 1; padding: 5px">
                          <div>
                            {{ item.materielNameEn }}
                          </div>
                          <div>
                            {{ item.materielCode }}
                          </div>
                        </div>
                      </div>

                    </template>
                  </el-popover>
                </div>
                <div>
                  <el-link
                      v-if="isFavor"
                      :underline="false"
                      type="primary"
                      class="operate-item"
                      @click="deleteItem(item)">
                    Delete
                  </el-link>
                  <el-link
                      v-if="!isFavor"
                      :underline="false"
                      type="primary"
                      class="operate-item"
                      @click="deleteItem(item)">
                    Delete
                  </el-link>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Diagram弹窗--BomCanvas   -->
  <el-dialog destroy-on-close v-model="dialogBomCanvasVisible" title="Bom Diagram" width="70%" top="1vh"
    :show-close="true" :before-close="handleBomCanvasModalClose">
    <BomCanvas :params="paramsBomCanvas" ref="bomCanvasRef" />

  </el-dialog>
</template>
<script setup lang="ts">
import { ref, watch, onMounted, inject } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElLoading, ElMessage } from "element-plus";
import BreadCrumb from "../../components/BreadCrumb.vue";
import { useRouteParamsStore } from "../../stores/useRouteParamsStore.js";
import SortIcon from "../../components/sortIcon.vue";
import BomCanvas from "../home/<USER>/bomCanvas.vue";
import {
  api_post_currentUserShopCarListPage,
  api_post_currentUserFavoriteslistPage,
  api_post_shopCart_delete,
  api_post_addToFavorites,
  api_post_addToCart,
  api_post_updCartCount,
  api_get_BomItems_usedIn,
  formatPrice
} from "../../utils/api";
const route = useRoute();
const router = useRouter();
const isFavor = ref(false);
const loading = ref(false);
const usedInVisible = ref({});
const currentUsedInData = ref({});
const userInfo = ref(
  JSON.parse(window.sessionStorage.getItem("userInfo")) || {}
);

const rpStore = useRouteParamsStore();
watch(
  () => router.currentRoute.value.path,
  (...toPath) => {
    const queryParams = route.query;
    isFavor.value = queryParams.type === "favor";
    //要执行的方法
  },
  { immediate: true, deep: true }
);
const base_downFileByPath_url = inject("$base_downFileByPath_url");
const checkAll = ref(false);
const commodityList = ref<{ [key: string]: any }[]>([]);
const currentSortKey = ref("");

/*  --BomCanvas组件下--*/
const bomCanvasRef = ref();
const paramsBomCanvas = ref({
  id: "",
  existsCart: "",
  existsFavorites: "",
  filePath: "",
  bomId: "",
  materielCode: "",
  materielNameEn: "",
  materielName: "",
  indexNo: 0,
  quantity: 0,
  amount: 0,
  price: 0
});
const dialogBomCanvasVisible = ref(false);
const handleBomCanvasModalClose = (done) => {
  dialogBomCanvasVisible.value = false;
};
const jumpDetail = (item) => {

  console.log(item);

  dialogBomCanvasVisible.value = true;
  paramsBomCanvas.value.filePath = item.filePath;
  paramsBomCanvas.value.bomId = item.bomId;
  paramsBomCanvas.value.materielCode = item.materielCode;
  paramsBomCanvas.value.materielNameEn = item.materielNameEn;
  paramsBomCanvas.value.materielName = item.materielName;
  paramsBomCanvas.value.quantity = item.quantity;
  paramsBomCanvas.value.price = item.unitPrice;
  paramsBomCanvas.value.indexNo = item.indexNo;
  paramsBomCanvas.value.id = item.bomItemsId;
  //购物车 收藏夹
  if (isFavor.value) {

    paramsBomCanvas.value.existsFavorites = "1";
    //注意：这里existsFavorites=1是购物车和收藏夹互相存在的意思
    paramsBomCanvas.value.existsCart = item.existsFavorites;
  } else {

    paramsBomCanvas.value.existsCart = "1";
    //注意：这里existsFavorites=1是购物车和收藏夹互相存在的意思
    paramsBomCanvas.value.existsFavorites = item.existsFavorites;
  }


};
/*  --BomCanvas组件上--*/

const usedInFunc = (materielCode) => {
  usedInVisible.value[materielCode] = !usedInVisible.value[materielCode];
  //currentUsedInData
  api_get_BomItems_usedIn({ materielCode: materielCode }).then((data) => {
    currentUsedInData.value[materielCode] = data;
  });
}

const getList = () => {
  loading.value = true;
  if (isFavor.value) {
    api_post_currentUserFavoriteslistPage()
      .then((res) => {
        //alert("--------Favorites--------------")
        console.log(res);
        commodityList.value = res;
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    api_post_currentUserShopCarListPage()
      .then((res) => {
        // alert("-----ShopCar-----------------")
        commodityList.value = res;
      })
      .finally(() => {
        loading.value = false;
      });
  }
};
onMounted(() => {
  getList();
});

const handleCheckAllChange = (val: boolean) => {
  commodityList.value.forEach((item) => {
    item.checked = val;
    item.cartList.forEach((el) => {
      el.checked = val;
    });
  });

  getCheckedList();
};

const handleCheckItemAllChange = (e, list) => {
  list.cartList.forEach((el) => {
    el.checked = e;
  });
  checkAll.value = commodityList.value.every((item) => item.checked);
};

const handleCheckItemChange = (e, commodityItem) => {
  const noCheckeds = commodityItem.cartList.filter((item) => !item.checked);
  if (!noCheckeds.length) {
    commodityItem.checked = true;
  } else {
    commodityItem.checked = false;
  }
};

const getCheckedList = () => {
  return commodityList.value.reduce((total, item) => {
    const current = item.cartList.filter((el) => el.checked);
    return [...total, ...current];
    /*`reduce` 方法会自动初始化 `total` 为下方第二参数提供的初始值 `[]`，item为当前遍历元素*/
  }, []);
};

const getTableIndex = (itemIndex, index) => {
  return (
    Array(itemIndex)
      .fill(null)
      .reduce(
        (total, current, currentIndex) =>
          total + (commodityList.value?.[currentIndex]?.cartList?.length || 0),
        0
      ) +
    index +
    1
  );
};

const onSort = ({ type, key }) => {
  currentSortKey.value = key;
  const sort = (sortArray) => {
    sortArray.sort((a, b) => {
      if (type === "asc") {
        return a[key].localeCompare(b[key]);
      } else {
        return b[key].localeCompare(a[key]);
      }
    });
  };
  sort(commodityList.value);
  commodityList.value.forEach((item) => {
    sort(item.cartList);
  });
};

const deleteItem = (item) => {
  api_post_shopCart_delete({
    id: item.id,
  }).then((res) => {
    if (res) {
      ElMessage.success("Deleted successfully");

      getList();
    }
  });
};

const deleteSelected = () => {
  const selectedList = getCheckedList();
  if (selectedList.length) {
    api_post_shopCart_delete({
      id: selectedList.map((item) => item.id).join(","),
    }).then((res) => {
      if (res) {
        ElMessage.success("Deleted successfully");
        commodityList.value = [];
        getList();
      }
    });
  } else {
    ElMessage.warning("Please select the items to be deleted");
  }
};

const getImageUrl = (array, type) => {
  const data = array.reduce((total: string[], current: string) => {
    const urlArray = current.split("/").reverse();
    const isProduct = type !== "all" ? urlArray[0].includes("product") : true;
    if (
      urlArray.length > 0 &&
      isProduct &&
      (urlArray[0].includes(".jpg") || urlArray[0].includes(".png"))
    ) {
      total.push(base_downFileByPath_url + current);
    }
    return total;
  }, []);
  return data;
};

const onCreate = () => {
  const selectedList = (commodityList || []).value.reduce((total, current) => {
    const currentList = current.cartList
      .filter((el) => el.checked)
      .map((item) => ({
        amount: item.quantity,
        ...item,
        shopId: item.id,
      }));
    if (currentList.length) {
      total.push({ ...current, cartList: currentList });
    }
    return total;
  }, []);
  rpStore.setParam("selectedList", selectedList);
  router.push("/createOrder");
};

const addCart = (row) => {
  api_post_addToCart([
    {
      amount: 1,
      bomItemsId: row.bomItemsId,
      productType: row.productType,
    },
  ]).then((res) => {

    if ("0" == res.code) {
      //row.existsCart="1";
      row.existsFavorites = "1";
      ElMessage.success({
        message: res.msg,
        duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
        offset: 150,
      });
    } else if (("1" == res.code)) {
      // row.existsCart="0";
      row.existsFavorites = "0";
      ElMessage.error({
        message: res.msg,
        duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
        offset: 150,
      });
    } else {
      ElMessage.error({
        message: res.msg,
        duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
        offset: 150,
      });

    }

  });
};

const addFavorit = (row) => {
  api_post_addToFavorites([
    {
      bomItemsId: row.bomItemsId,
      productType: row.productType,
    },
  ]).then((res) => {

    if ("0" == res.code) {
      // row.existsCart="1";
      row.existsFavorites = "1";
      ElMessage.success({
        message: res.msg,
        duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
        offset: 150,
      });
    } else if (("1" == res.code)) {
      // row.existsCart="0";
      row.existsFavorites = "0";
      ElMessage.error({
        message: res.msg,
        duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
        offset: 150,
      });
    } else {
      ElMessage.error({
        message: res.msg,
        duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
        offset: 150,
      });

    }


  });
};

const add_Cart_Favorit = (row) => {
  var productType = row.productType;
  if ("part" == productType) {
    addFavorit(row);
  } else {
    addCart(row);
  }
};
const amountChange = async (item) => {

  api_post_updCartCount([
    {
      id: item.id,
      amount: item.amount,
    },
  ]).then((res) => {
    if (res) {
      item.amountOfMoney = parseFloat(
        (parseFloat(item.unitPrice) * parseFloat(item.amount)).toFixed(2)
      );
      //ElMessage.success("Modification of amount");
    }
  });
};
</script>
<style lang="less" scoped>

/*调节多选框方框的大小*/
::v-deep .item-all-checkbox .el-checkbox__inner {
  width: 18px;  /* 调整宽度 */
  height: 18px; /* 调整高度 */
  /*font-size: 16px;*/ /* 调整勾选图标的大小 */
}
/*调节多选框方框的大小*/
::v-deep .el-checkbox__inner {
  width: 18px;  /* 调整宽度 */
  height: 18px; /* 调整高度 */
}

::v-deep .el-checkbox__inner {
  border-width: 2px; // 调整边框粗细
  border-color: #f56c6c; // 可选：调整边框颜色
  border-style: solid; // 确保边框样式为实线
}

.cart-content {
  padding: 12px;

  .cart-control {
    display: flex;
    align-items: center;
    width: 100%;
    padding-bottom: 16px;
    padding-left: 16px;
  }

  .shopping-cart-item-all {
    border-radius: 8px;
    margin-bottom: 16px;

    &.head-item {
      height: 39px;
      align-items: center;
      background-color: #f5f5f5;
      margin: 10px 0;
      font-size: 14px;
      margin-bottom: 10px;
      border: none;

      .cart-all-item-view {
        display: flex;
      }
    }

    .item-all-name {
      align-items: center;
      background-color: #f3f6f8;
      border: 1px solid #f0f3f5;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      display: flex;
      flex-direction: row;
      height: 32px;
      overflow: hidden;
      color: #11192d;
      font-size: 12px;
      font-weight: 500;
    }

    .item-all-checkbox {
      margin-right: 16px;
    }

    .item-all-content {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-color: #f0f3f5;
      border-style: solid;
      border-width: 1px;
      position: relative;
      padding-left: 16px;
      border-left: none;

      .cart-all-item {
        align-items: flex-start;
        display: flex;
        position: relative;
        align-items: center;
        padding: 6px 0;

        .item-all-checkbox {
          // width: 48px;
        }

        .cart-all-item-info {
          flex: 1;
          align-items: center;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          margin-right: 16px;
          padding-top: 16px;

          .cart-all-item-img {
            width: 46px;
            height: 46px;
            margin-right: 12px;
            background-color: rgba(0, 0, 0, 0.02);
            position: relative;
          }

          .cart-all-item-name {
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            color: #11192d;
            display: -webkit-box;
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            margin-bottom: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .cart-all-item-view {
          margin-right: 16px;

          &.index {
            width: 60px;
          }

          &.materierCode {
            flex: 1;
          }

          &.quantity {
            flex: 0.5;
          }

          &.amount {
            flex: 1.2;
          }

          &.unitPrice {
            flex: 0.6;
            color: #ff5000;
            font-size: 18px;
            font-weight: 600;
          }

          &.remarks {
            flex: 3;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            margin-bottom: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          &.operate {
            margin-right: 0;
            width: 180px;

            >div {
              display: flex;
              align-items: center;
              margin-right: 10px;
            }

            .operate-item {
              display: block;
            }
          }
        }
      }
    }
  }
}

.wb-image {
  /deep/ .el-image-viewer__canvas {
    .el-image-viewer__img {
      background: #fff;
    }
  }
}
</style>
