<template>
  <div ref="pdfContainer" class="pdf-container"></div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
// import { getDocument, GlobalWorkerOptions } from 'pdfjs-dist'
// import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.min.js'

// 设置pdf.js的worker路径
GlobalWorkerOptions.workerSrc = pdfjsWorker

// 接收父组件传入的 props
const props = defineProps({
  pdfUrl: {
    type: String,
    required: true,
  },
})

// 创建一个ref用于指向PDF容器
const pdfContainer = ref(null)

// 在组件挂载时加载并渲染PDF
onMounted(async () => {
  if (props.pdfUrl) {
    const loadingTask = getDocument(props.pdfUrl)
    const pdf = await loadingTask.promise

    for (let pageNumber = 1; pageNumber <= pdf.numPages; pageNumber++) {
      const page = await pdf.getPage(pageNumber)
      renderPage(page)
    }
  }
})

// 渲染单页并将其添加到容器中
const renderPage = async (page) => {
  const viewport = page.getViewport({ scale: 1.5 })
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')

  canvas.height = viewport.height
  canvas.width = viewport.width
  pdfContainer.value.appendChild(canvas)

  const renderContext = {
    canvasContext: context,
    viewport: viewport,
  }
  await page.render(renderContext).promise
}
</script>

<style scoped>
.pdf-container {
  width: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
