{"name": "website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:usa": "cross-env VITE_COUNTRY_OF_USE=USA vite", "dev:en": "cross-env VITE_COUNTRY_OF_USE=en vite", "dev:cn": "cross-env VITE_COUNTRY_OF_USE=cn vite", "build": "vite build", "build:usa": "cross-env VITE_COUNTRY_OF_USE=USA vite build", "build:en": "cross-env VITE_COUNTRY_OF_USE=en vite build", "build:cn": "cross-env VITE_COUNTRY_OF_USE=cn vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^12.7.0", "axios": "^1.7.3", "element-plus": "^2.8.0", "pdfjs-dist": "^4.5.136", "pinia": "^2.2.1", "pinia-plugin-persistedstate": "^3.2.1", "vue": "^3.4.31", "vue-router": "^4.4.3"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.5", "cross-env": "^7.0.3", "less": "^4.2.0", "less-loader": "^12.2.0", "vite": "^5.3.4"}}