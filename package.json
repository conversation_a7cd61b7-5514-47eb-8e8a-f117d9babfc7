{"name": "website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^12.7.0", "axios": "^1.7.3", "element-plus": "^2.8.0", "pdfjs-dist": "^4.5.136", "pinia": "^2.2.1", "pinia-plugin-persistedstate": "^3.2.1", "vue": "^3.4.31", "vue-router": "^4.4.3"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.5", "less": "^4.2.0", "less-loader": "^12.2.0", "vite": "^5.3.4"}}