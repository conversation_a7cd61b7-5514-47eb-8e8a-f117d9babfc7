<template>
  <div class="wran-nav" style="height:33px;background-color: #e32620;">
    <h3>
      <img src="../../assets/images/nav/w.svg" alt="Orders Icon" />
      Catalog
      <text v-if="!isHome"></text>
    </h3>
<!--    <div style="padding: 5px 0px 0px 0px;">
      <img src="../../assets/images/nav/other.svg" alt="Orders Icon" />
    </div>-->
  </div>
  <div class="container">
    <div class="brand-list">
      <div
        class="brand-list-item"
        v-for="(item, index) in brand_list"
        @click="toProduct(item)"
      >
        <img :src="`${base_img_url}${item.fileName}.png?t=${Date.now()}`" />

    <!--<img :src="`${base_img_url}${item.fileName}.png`" />-->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref,reactive, onMounted,inject} from 'vue'
import { api_login, api_get_brand } from '../../utils/api.js'
import { useRouter } from 'vue-router'
import { useRouteParamsStore } from '../../stores/useRouteParamsStore.js'
const router = useRouter()
const brand_list = ref([])
const base_img_url = inject('$base_img_url')

const rpStore = useRouteParamsStore()

const getBrandList = async () => {
  const resp = await api_get_brand()
  brand_list.value = resp
  console.log(`output->resp`, resp)
}

const toProduct = (item) => {
  rpStore.setParams({
    id: item.id,
    fileName: item.fileName,
    filePath: item.filePath,
    type: item.type,
  })
  router.push({
    path: '/product'
  })

}

onMounted(async () => {
  // await api_login({
  //   password: 'admin0123',
  //   mobile: 'admin',
  // })
  getBrandList()
})
</script>

<style lang="less" scoped>
.brand-list {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 3px;
  width: 85%;

  .brand-list-item {
    display: flex;
    flex-direction: column;
    img {
      width: 85%;
    }
    .card-header {
      padding: 24px 0 12px 12px;
      width: 100%;
      background-color: #ececec;
      display: flex;
      flex-direction: column;
      text:nth-child(1) {
        font-weight: bold;
        font-size: 20px;
        color: #5f666a;
      }
      text:nth-child(2) {
        font-size: 16px;
        color: #5f666a;
      }
    }
    .card-body {
      padding: 12px;
      min-height: 400px;
      border: 2px solid #ececec;
      img {
        height: 160px;
        /*width: 100%;*/
      }
    }
    .card-footer {
      padding: 16px 12px;
      background-color: #ececec;
      font-size: 20px;
      color: #5f666a;
    }
  }
}
</style>
