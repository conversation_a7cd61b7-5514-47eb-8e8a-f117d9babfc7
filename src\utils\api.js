import { get, post,postFormData } from "./axios";
import axios from "axios";
import {$base_host} from "../main.js";
import {ElLoading, ElMessage} from "element-plus";
export const api_login = async (data = {}) => {
  const url = "/wx/pda/login/users/sparts";
  return await post(url, data);
};

export const api_get_brand = async (data = {}) => {
  const url = "/a/pmc/catalog/pmcMaterielBomCatalog/brand";
  return await post(url, data);
};

export const api_get_product = async (data = {}) => {
  //catalog是从父级点击进入子级,因此传入的是父级id
  //后台查出所有子集 AND a.parent_ids LIKE
  //传type将限定 子集 的数据
  const url = "/a/pmc/catalog/pmcMaterielBomCatalog/catalogByType";
  return await post(url, data);
};

export const api_get_catalog_byId = async (data = {}) => {
  const url = "/windows/pythonQT/view/getCatalogById";
  return await post(url, data);
};
export const api_get_catalog_product = async (data = {}) => {
  const url = "/windows/pythonQT/view/product";
  return await post(url, data);
};

export const api_get_menu = async () => {   
  const url = "/windows/pythonQT/view/listData";
  return await get(url, {});
};

export const api_get_bom_items_recommend = async (data = {}) => {
  const url = "a/pmc/catalog/pmcMaterielBomItemsRecommend/listData";
  return await post(url, data);
};


export const api_url_bom_items_excel = $base_host+"/windows/pythonQT/view/bomItemsExcel?bomId=";


//usedIn
export const api_get_BomItems_usedIn = async (data = {}) => {
  const url = "a/pmc/catalog.item/pmcMaterielBomItems/usedIn";
  return await post(url, data);
};

//配件表物料明细接口
export const api_get_table_data = async (data = {}) => {
  const url = "a/pmc/catalog.item/pmcMaterielBomItems/listData";
  return await post(url, data);
};

export const api_get_coordinate = async (data = {}) => {
  const url = "a/pmc/catalog/pmcMaterielBomItemsJsonCoordinate/listData";
  return await post(url, data);
};

export const api_save_coordinate = async (data = {}) => {
  const url = "/a/pmc/catalog/pmcMaterielBomItemsJsonCoordinate/save";
  return await post(url, data, { "Content-Type": "application/json" }, false);
};

export const api_delete_coordinate = async (data = {}) => {
  const url = "/a/pmc/catalog/pmcMaterielBomItemsJsonCoordinate/delete";
  return await post(url, data);
};

export const api_post_login = async (data = {}) => {
  const url = "/wx/pda/login/users/sparts";
  return await post(url, data);
};

/** 查询购物车 收藏夹 */
export const api_post_listData_currentUserShopCar = async (data = {}) => {
  const url = "/a/pmc/shopCart/listData_currentUser";
  return await post(url, data);
};

/** 查询当前用户购物车 */
export const api_post_currentUserShopCarListPage = async (data = {}) => {
  const url = "/a/pmc/shopCart/currentUserShopCarListPage";
  return await post(url, data);
};

/** 查询当前用户收藏夹 */
export const api_post_currentUserFavoriteslistPage = async (data = {}) => {
  const url = "/a/pmc/shopCart/currentUserFavoriteslistPage";
  return await post(url, data);
};

/*updCartCount*/
export const api_post_updCartCount = async (data = {}) => {
  const url = "/a/pmc/shopCart/updCartCount";
  return await post(url, data, { "Content-Type": "application/json" }, false);
};

/** 加入购物车
 */
export const api_post_addToCart = async (data = {}) => {
  const url = "/a/pmc/shopCart/addToCart";
  return await post(url, data, { "Content-Type": "application/json" }, false);
};

/** 加入收藏夹*/
export const api_post_addToFavorites = async (data = {}) => {
  const url = "/a/pmc/shopCart/addToFavorites";
  return await post(url, data, { "Content-Type": "application/json" }, false);
};

export const api_post_shopCart_delete = async (data = {}) => {
  const url = "/a/pmc/shopCart/delete";
  return await post(url, data);
};


/*通知*/
export const api_post_pmcNotice_noticeNumber = async (data = {}) => {
  const url = "/a/pmc/notice/pmcNotice/noticeNumber";
  return await post(url, data);
};

export const api_post_pmcNotice_myNotice = async (data = {}) => {
  const url = "/a/pmc/notice/pmcNotice/myNotice";
  return await post(url, data);
};

export const api_post_pmcNotice_readNotice = async (data = {}) => {
  const url = "/a/pmc/notice/pmcNotice/readNotice";
  return await post(url, data);
};

export const api_post_pmcNotice_listData = async (data = {}) => {
  const url = "/a/pmc/notice/pmcNotice/listData";
  return await post(url, data);
};



/**
 *        零件搜索-
 */
export const api_post_spartPage = async (data = {}) => {
  const url = "/a/pmc/catalog/pmcMaterielBomCatalog/spartPage";
  return await post(url, data);
};
/**
 * 产品搜索
 */
export const api_post_productPage = async (data = {}) => {
  const url = "/a/pmc/catalog/pmcMaterielBomCatalog/productPage";
  return await post(url, data);
};

/**
 * 下单
 */
export const api_post_shopCarOrderSave = async (data = {}) => {
  const url = "/a/pmc/shopCart/shopCarOrderSave";
  return await post(url, data, { "Content-Type": "application/json" }, false);
};

/**
 * 订单列表
 */
export const api_post_shopOrderListPage = async (data = {}) => {
  const url = "/a/pmc/shopOrder/listPageHead";
  return await post(url, data);
};
/**
 * 订单信息明细
 */
export const api_post_queryShopOrderInfo = async (data) => {
  const url = "/a/pmc/shopOrder/queryShopOrderInfo";
  return await post(url, data);
};
export const api_post_queryShopOrderInfoView = async (data) => {
  const url = "/windows/pythonQT/view/queryShopOrderInfo";
  return await post(url, data);
};

/**/
export const api_url_orderPackingList_excel = $base_host+"/a/pmc/shopOrder/orderExcelPackingList?logisticsNumber=";
/*导出订单*/
export const api_url_orderDetail_excel = $base_host+"/a/pmc/shopOrder/orderExcelShop?orderId=";
/*发票PDF*/
export const api_url_generateInvoice_pdf = $base_host+"/a/pmc/shopOrder/generatePdfInvoice?orderId=";

export const api_generateInvoice_pdf = async (data) => {
  const url = "/a/pmc/shopOrder/generatePdfInvoice";
  return await post(url, data);
};

/**
 * 确认订单
 */ export const api_post_confirmOrder = async (orderId) => {
  const url = "/a/pmc/shopOrder/confirmOrder?orderId=" + orderId;
  return await post(url, {}, { "Content-Type": "application/json" }, false);
};

/**
 * 删除订单
 */
export const api_post_deleteOrder = async (orderId) => {
  const url = "/a/pmc/shopOrder/deleteOrder_shopMall?orderId=" + orderId;
  return await post(url, {}, { "Content-Type": "application/json" }, false);
};



/**
 * 修改密码
 */
export const api_post_modifyPwd = async (data) => {
  const url = "/a/sys/user/modifyPwd";
  return await post(url, data, { "Content-Type": "application/json" }, false);
};

/*updateProfile*/
export const api_post_updateProfile = async (data) => {
  const url = "/a/sys/user/updateProfile";
  return await post(url, data);
};

/**
 * 退出登录
 */
export const api_post_logout2 = async () => {
  const url = "/a/logout2";
  return await post(url, {}, { "Content-Type": "application/json" }, false);
};

/*获取商城用户列表*/
export const api_dealer_account_list = async (data = {}) => {
  const url = "/a/sys/user/listDataMall?userType=2";
  return await post(url, data);
};

/*获取商城公司列表*/
export const api_dealer_company_list = async (data = {}) => {
  const url = "/a/pmc/basic/companyInfo/list/data?bizType=2";
  return await post(url, data);
};

/*获取用户信息*/
export const api_post_show_data = async (data = {}) => {
  const url = "/a/sys/user/show/data";
  return await post(url, data);
};

/*获取用户信息*/
export const api_post_userInfoData = async (data = {}) => {
  const url = "/a/sys/user/infoData";
  return await post(url, data);
};

/*查询客户*/
export const api_post_searchUserLinkMan = async (data = {}) => {
  const url = "/a/pmc/basic/companyLinkman/searchUserLinkMan";
  return await post(url, data);
};

export const api_post_saveUserLinkMan = async (data = {}) => {
  const url = "/a/pmc/basic/companyLinkman/saveUserLinkMan";
  return await post(url, data);
};

export const api_post_deleteUserLinkMan = async (data = {}) => {
  const url = "/a/pmc/basic/companyLinkman/remove";
  return await post(url, data);
};

/*register product*/
export const api_post_registerlistPage = async (data = {}) => {
  const url = "/a/pmc/claims/pmcClaimsRegisterProduct/registerlistPage";
  return await post(url, data);
};

export const api_post_saveRegisterProduct = async (data = {}) => {
  const url = "/a/pmc/claims/pmcClaimsRegisterProduct/saveRegisterProduct";
  // 设置 Content-Type 为 undefined，让浏览器自动处理 multipart/form-data
  return await postFormData(url, data);
};

export const api_post_confirmRegisterProduct = async (data = {}) => {
  const url = "/a/pmc/claims/pmcClaimsRegisterProduct/confirmRegisterProduct";
  // 设置 Content-Type 为 undefined，让浏览器自动处理 multipart/form-data
  return await post(url, data);
};

export const api_post_deleteRegisterProduct = async (data = {}) => {
  const url = "/a/pmc/claims/pmcClaimsRegisterProduct/delete";
  return await post(url, data);
};

export const api_post_saveClaimsRecord = async (data = {}) => {
  const url = "/a/pmc/claims/pmcClaimsRecord/saveClaimsRecord";
  // 设置 Content-Type 为 undefined，让浏览器自动处理 multipart/form-data
  return await postFormData(url, data);
};

export const api_post_DeleteClaimsRecord = async (data = {}) => {
  const url = "/a/pmc/claims/pmcClaimsRecord/delete";
  return await post(url, data);
};

/**
 * 预览文件
 */
export const previewFile = async (path) => {
  axios({
    method: "get",
    url: "/sparts-wb/a/file/downFileByPath?filePath=" + path,
    responseType: "blob",
    headers: {
      accept: "application/pdf",
      "Content-Disposition": "inline",
      "Content-Type": "application/pdf",
    },
  })
    .then((response) => {
      const url = URL.createObjectURL(response.data);
      window.open(url, "_blank");
    })
    .catch((error) => {
      console.error(error);
    });
};



/*
公共js方法
*/
/*
公共js方法
*/

//格式化价格
export const formatPrice =(value) => {
  if (isNaN(value)) {
    return '0.00'; // 如果值不是数字，返回默认值
  }

  // 使用 Intl.NumberFormat 格式化数字
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2, // 保留两位小数
    maximumFractionDigits: 2, // 保留两位小数
  }).format(value);
}

// 从树中根据指定字段和值查找节点
export const findNodeByField_tree = (tree, field, value) => {
  for (let i = 0; i < tree.length; i++) {
    // 判断当前节点是否匹配
    if (tree[i][field] === value) {
      return tree[i];
    }
    // 如果当前节点有子节点，则递归查找
    if (tree[i].children) {
      const found = findNodeByField_tree(tree[i].children, field, value);
      if (found) {
        return found;
      }
    }
  }
  return null; // 如果未找到，返回null
};

//从tree中找id
export const findNodeById_tree =(tree, id) => {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].id === id) {
      return tree[i];
    }
    if (tree[i].children) {
      const found = findNodeById_tree(tree[i].children, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
}

//-------导出excel公用方法--------------
export const loadDown =(url) => {
  const loading = ElLoading.service({
    text: "loading...",
    background: "rgba(255, 255, 255, 0.7)",
  });
  var xhr = new XMLHttpRequest();
  xhr.open("POST", url, true); // 也可以使用POST方式，根据接口
  xhr.responseType = "blob";
  xhr.onload = function () {
    if (this.status === 200) {
      var blob = this.response;
      var reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onload = function (e) {
        loading.close();
        var headerName = xhr.getResponseHeader("Content-disposition");
        var fileName = decodeURIComponent(headerName).substring(20);
        //如果是IE，使用msSaveOrOpenBlob
        if (window.navigator.msSaveOrOpenBlob) {
          navigator.msSaveOrOpenBlob(blob, fileName);
        } else {
          var a = document.createElement("a");
          a.download = fileName;
          a.href = e.target.result;
          // 修复firefox中无法触发click
          document.body.appendChild(a);
          a.click();
          // 移除添加的元素
          document.body.removeChild(a);
        }
      };
    } else {
      loading.close();
      ElMessage.error("Erro!");
    }
  };
  var formdata = new FormData();
  xhr.send(formdata);
};


export const downloadPDF = (url) => {
  const loading = ElLoading.service({
    text: "下载中...",
    background: "rgba(0, 0, 0, 0.5)"
  });

  return new Promise((resolve) => {
    const xhr = new XMLHttpRequest();
    xhr.open("GET", url, true);
    xhr.responseType = "blob";

    xhr.onload = function() {
      loading.close();
      console.log("HTTP status:", this.status); // 打印 HTTP 状态码
      if (this.status === 200) {
        const blob = this.response;
        console.log("Blob size:", blob.size); // 打印 Blob 大小
        const header = xhr.getResponseHeader('Content-disposition') || '';
        console.log("Content-Disposition header:", header); // 打印 Content-Disposition 头
        const contentType = xhr.getResponseHeader('Content-Type');
        console.log("Content-Type header:", contentType); // 打印 Content-Type 头
        const fileName = decodeURIComponent(header.split('filename=')[1] || 'document.pdf');

        if (window.navigator.msSaveOrOpenBlob) {
          navigator.msSaveOrOpenBlob(blob, fileName);
        } else {
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = fileName;
          document.body.appendChild(link);
          link.click();
          setTimeout(() => {
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
          }, 100);
        }
        resolve(true);
      } else {
        ElMessage.error("下载失败 (\$\{this.status})");
          resolve(false);
      }
  };

    xhr.onerror = () => {
      loading.close();
      ElMessage.error('网络错误');
      resolve(false);
    };

    xhr.send();
  });
};

//countryOfuse  ----USA  cn  en
export const countryOfuse = "USA";