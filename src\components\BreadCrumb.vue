<template>
  <div class="breadcrumb" style="height:33px;background-color: #e32620;">
    <a href="#" class="breadcrumb-item" @click="toBrand">
      <img src="../assets/images/nav/home.svg" alt="Home" />
      <img src="../assets/images/nav/arrow-right.svg" alt="" />
    </a>

    <a v-if="breadcrumbStore.breadcrumbs.length>0" href="#" class="breadcrumb-item" v-for="(item, index) in breadcrumbStore.breadcrumbs"
      @click="breadcrumbListClick(item, index)" style="color: #fff;">
      {{ item.fileName }}
      <img src="../assets/images/nav/arrow-right.svg" v-if="index !== breadcrumbStore.breadcrumbs.length - 1" alt="" />
    </a>
    <a v-if="list && breadcrumbStore.breadcrumbs.length>0" href="#" class="breadcrumb-item" style="color: #fff;">
      <img src="../assets/images/nav/arrow-right.svg"  alt="" />
    </a>
    <a v-if="list" href="#" class="breadcrumb-item" v-for="(item, index) in list || []"
       @click="breadcrumbListClick(item, index, item.url)" style="color: #fff;">
      {{ item.name }}
      <img src="../assets/images/nav/arrow-right.svg" v-if="index !== (list || []).length - 1" alt="" />
    </a>

<!--    <div class="breadcrumb-actions">
      <a href="#" class="breadcrumb-action">
        <img src="../assets/images/nav/favor_fill.svg" alt="Favorite" />
      </a>
      <a href="#" class="breadcrumb-action">
        <img src="../assets/images/nav/other.svg" alt="Other" />
      </a>
    </div>-->
  </div>
</template>

<script setup>
import { onMounted, ref, computed, reactive } from 'vue'
import { api_get_product } from '../utils/api'
//useRoute 用于获取当前路由信息，而 useRouter 用于执行导航操作
import { useRoute, useRouter } from 'vue-router'
import { useBreadcrumbStore } from '../stores/useBreadcrumbStore.js'
import { useRouteParamsStore } from '../stores/useRouteParamsStore.js'
/*<!-- 这里假设 dataList 是父组件中的一个数组数据 --> <MyComponent :list="dataList" />*/
const props = defineProps(['list'])
const getPrevType = (type) => {
  switch (type) {
    case 'productType':
      return 'brand'
      break
    case 'model':
      return 'productType'
      break
    case 'bom':
      return 'model'
      break
    case undefined:
      return 'bom'
    default:
      break
  }
}

const router = useRouter()
const route = useRoute()
const breadcrumbStore = useBreadcrumbStore()
const rpStore = useRouteParamsStore()

const toBrand = () => {
  router.push({
    path: '/home',
  })
  breadcrumbStore.clearBreadcrumbs()
}

const breadcrumbListClick = async (item, index, url = '') => {
  if (url) {
    // 错误方式：直接使用 path
   // router.push({ path: url })
    // 正确方式：解析 URL 参数
    const [path, search] = url.split('?')
    const query = search
        ? Object.fromEntries(new URLSearchParams(search).entries())
        : {}
    router.push({
      path: path,
      query: {
        ...route.query, // 保留现有参数
        ...query        // 添加新参数
      }
    })
    return
  }

  breadcrumbStore.breadcrumbs.length = index + 1
  console.log(`route.name`, route.name)

  if (item.type == "toDetail") {

    router.push({
      path: '/detail',
      query: {
        params: item.filePath,
      },
    })

  }else if(route.name !== 'Product'){
    console.log(`output->item.type`, item)
    await rpStore.setParams({
      id: item.id,
      fileName: item.fileName,
      filePath: item.filePath,
      type: getPrevType(item.type),
      detail: true,
      
    })
    router.push({
      path: '/product',
    })
    console.log(breadcrumbStore.breadcrumbs)
  } else {
    //当前在product页面点击面包屑
    const resp = await api_get_product({
      id: item.id,
      type: item.type,
    })
    breadcrumbStore.setProducts(resp)
  }
}
</script>
