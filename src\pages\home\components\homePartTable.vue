<template>
  <div class="cart-list">
    <div class="head-all-item small">
      <div class="item-all-image" style="width: 140px; padding-left: 16px;font-weight: bold;">
        Equipment(Fitted On)
      </div>
      <div
        style="display: flex; align-items: center; padding-left: 16px; flex: 1"
      >
        <div class="head-all-item-view operate"></div>
        <div class="head-all-item-view quantity bold" style="text-align: center">Quantity</div>
        <div class="head-all-item-view unitPrice bold" style="text-align: center">Price</div>
        <div class="head-all-item-view quantity bold">Description</div>
        <div class="head-all-item-view quantity bold">Components</div>
        <div class="head-all-item-view materierCode bold">Part Number</div>
<!--        <div class="head-all-item-view quantity">Name_Chinese</div>-->


      </div>
    </div>
    <el-scrollbar>
      <div>
        <div
          class="shopping-cart-item-all"
          v-for="(commodityItem, index) in partData"
        >
          <div class="item-all-name small">
            Model:&nbsp;&nbsp;&nbsp;
            <span style="text-decoration: underline">
              {{ commodityItem.model }}
            </span>&nbsp;&nbsp;&nbsp;

            Service_Code:&nbsp;&nbsp;&nbsp;
            <span style="text-decoration: underline">
              {{ commodityItem.serviceCode }}
            </span>&nbsp;&nbsp;&nbsp;

            Info:&nbsp;&nbsp;&nbsp;
            <span style="text-decoration: underline" class="truncate-text" >
              {{ commodityItem.materielSpecification }}
            </span>
          </div>
          <div style="display: flex; align-items: flex-start">
            <div style="min-width: 140px; max-width: 140px; padding: 0 10px">
              <el-image
                class="wb-image"
                :src="getImageUrl(commodityItem.productFileList || [])[0] || ''"
                :preview-src-list="
                  getImageUrl(commodityItem.productFileList || [], 'all')
                "
                style="width: 100%; height: auto"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :initial-index="4"
                fit="cover"
              />
            </div>
            <el-scrollbar max-height="150" style="flex: 1">
              <div class="item-all-content">
                <div
                  class="cart-all-item"
                  v-for="(item, index) in commodityItem.spartList || []"
                >
                  <div class="cart-all-item-view operate">
                    <el-button
                        @click="jumpDetail(item)"
                        link
                        type="primary"
                        size="small"
                        style="font-size: 14px; font-weight: 600;text-decoration: underline;"
                    >
                      Expl.View
                    </el-button>

                    <el-button
                      link
                      type="primary"
                      size="small"
                      @click.prevent="addFavorit(item, 'part')"
                      :loading="item.favoritLoad"
                    >
                        <img v-if="item.existsFavorites == '1'"
                               src="../../../assets/images/main/favorites.svg"
                              alt="+"
                              height="15px"
                        />
                        <img v-else
                             src="../../../assets/images/main/favoritesEmpty.svg"
                             alt="+"
                             height="15px"
                        />
                    </el-button>

                  </div>

                  <div class="cart-all-item-view quantity">
                       <el-input-number
                        v-model="item.amount"
                        :min="1"
                           @change="amountChange(item)"
                        />
                  </div>
                  <div class="cart-all-item-view unitPrice" >
                        <div style="display: flex; justify-content: center; align-items: center;gap: 5px;">
                                  <!-- 价格部分 -->
                                  <div style="width: 100px;">
                                      <span style="color: #ff5000; font-size: 16px; font-weight: 600; flex: 1;">
                                        {{ userInfo.currency === '1' ? '¥' : userInfo.currency === '2' ? '$' : userInfo.currency === '3' ? '€' : '' }}
                                        {{ formatPrice(item.totalPrice) }}
                                      </span>
                                  </div>

                                  <!-- 按钮图标组合 -->
                                  <div style="display: flex;  gap: 10px">
                                      <a @click="addCart(item, 'part')" style="display: flex; align-items: center">
                                        <img
                                            v-if="item.existsCart == '1'"
                                            src="../../../assets/images/main/purCarRed.svg"
                                            alt="+"
                                            height="15px"
                                            style="vertical-align: middle; position: relative; top: -1px"
                                        />
                                        <img
                                            v-else
                                            src="../../../assets/images/main/purCarRedEmpty.svg"
                                            alt="+"
                                            height="15px"
                                            style="vertical-align: middle; position: relative; top: -1px"
                                        />
                                      </a>
                                      <el-button
                                          type="danger"
                                          size="small"
                                          @click.prevent="addCart(item, 'part')"
                                          :loading="item.buyLoad"        style="min-width: 100px"
                                      >
                                        <span v-if="item.existsCart == '1'">Remove</span>
                                        <span v-else>Add To Cart</span>
                                      </el-button>
                                  </div>
                        </div>
                  </div>

                  <div class="cart-all-item-view quantity"
                       style="margin-left: 30px;">
                    {{ item.materielNameEn }}
                  </div>
                  <div class="cart-all-item-view quantity">
                    {{ item?.filePath?.split("/")?.pop()?.replace(".pdf","") }}
                  </div>
                  <div class="cart-all-item-view materierCode">
                      {{ item.materielCode }}
                      <div   v-if="userInfo.userType == 1"
                             style="color:rgb(65,155,255);font-size:15px;text-decoration: underline">
                              <span  v-if="userInfo.userType == 1 && item.warehouseAmount"
                                     style="color:#f56c6c;text-decoration: underline">
                                     stock:&nbsp;{{ formatStock(item.warehouseAmount) }}&nbsp;
                                    <span v-if="item.shelfCode">(&nbsp;{{ item.shelfCode }}&nbsp;)</span>
                              </span>
                      </div>
                  </div>
            <!--  <div class="cart-all-item-view quantity">
                    {{ item.materielName }}
                  </div>-->
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
        <div
          v-if="partData.length <= 0"
          style="text-align: center; margin-top: 20px; color: #a8abb2"
        >
          NO DATA
        </div>
      </div>
    </el-scrollbar>
  </div>
  <div style="display: flex; justify-content: flex-end; margin-top: 8px">
    <el-pagination
      layout="prev, pager, next"
      :total="page.total"
      :page-size="page.size"
      @current-change="currentChange"
      :current-page="page.current"
    />
  </div>

  <!-- Diagram弹窗--BomCanvas   -->
  <el-dialog
      destroy-on-close
      v-model="dialogFormVisible"
      title="BOM Diagram"
      width="70%"
      top="1vh"
      :show-close="true"
      :before-close="handleModalClose"
  >
    <BomCanvas :params="params" ref="bomCanvasRef" />

  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, defineProps } from "vue";
import {
  api_post_spartPage,
  api_post_productPage,
  api_post_addToFavorites,
  api_post_addToCart,
  formatPrice
} from "../../../utils/api";
import { ElLoading, ElMessage } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { useBreadcrumbStore } from "../../../stores/useBreadcrumbStore.js";
const base_downFileByPath_url = inject("$base_downFileByPath_url");

import BomCanvas from "./bomCanvas.vue";


const breadcrumbStore = useBreadcrumbStore();
const router = useRouter();
const props = defineProps({
  params: {
    type: Object,
    default: null,
  },
});
const page = ref({
  current: 1,
  total: 10,
  size: 300,
});
const partData = ref([]);
const userInfo = ref(
    JSON.parse(window.sessionStorage.getItem("userInfo")) || {}
);

/*  --BomCanvas组件下--*/
const bomCanvasRef = ref();
const params = ref({
  id:"",
  existsCart:"",
  existsFavorites:"",
  filePath: "",
  bomId:"",
  materielCode:"",
  materielNameEn:"",
  materielName:"",
  indexNo:0,
  quantity:0,
  amount:0,
  price:0
});
const dialogFormVisible = ref(false);
const handleModalClose = (done) => {
  dialogFormVisible.value=false;
};
const jumpDetail = (item) => {

  dialogFormVisible.value=true;
  params.value.filePath=item.filePath;
  params.value.bomId=item.bomId;
  params.value.materielCode=item.materielCode;
  params.value.materielNameEn=item.materielNameEn;
  params.value.materielName=item.materielName;
  params.value.quantity=item.quantity;
  params.value.price=item.price;
  params.value.indexNo=item.indexNo;
  params.value.id = item.id;
  //购物车 收藏夹
  params.value.existsFavorites=item.existsFavorites;
  params.value.existsCart=item.existsCart;

};
/*  --BomCanvas组件上--*/


onMounted(() => {
  getTableList();
});




const getTableList = () => {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(255, 255, 255, 0.7)",
  });

  //配件搜索第一层级的id 默认去除
  var param_parentId_part = "";
  if(props.params.id
      //Equipment 9033c9a9872d4106bf2f49f511210878
      //Engine 533c4332a0b24a959808a279766bb550
      && (props.params.id == "9033c9a9872d4106bf2f49f511210878" || props.params.id == "533c4332a0b24a959808a279766bb550")){
    param_parentId_part = "";
  }else{
    param_parentId_part = props.params.id;
  }

  var param_part ={
    parentId: param_parentId_part,
    key: props.params.partSearchKey || "",
    pageNo: page.value.current,
    pageSize: page.value.size,
  };

  api_post_spartPage(param_part)
    .then((res) => {
      partData.value = res.data || [];
    })
    .finally(() => {
      loading.close();
    });

};

// 新增的库存数量格式化方法
const formatStock = (value) => {
  if (!value) return '0'
  const number = Number(value)
  // 判断小数部分是否为零
  return number % 1 === 0
      ? number.toFixed(0)  // 整数部分
      : number.toFixed(2).replace(/\.?0+$/, '') // 保留非零小数并去除末尾零
}

const getImageUrl = (array, type) => {
  const data = array.reduce((total: string[], current: string) => {
    const urlArray = current.split("/").reverse();
    const isProduct = type !== "all" ? urlArray[0].includes("product") : true;
    if (
      urlArray.length > 0 &&
      isProduct &&
      (urlArray[0].includes(".jpg") || urlArray[0].includes(".png"))
    ) {
      total.push(base_downFileByPath_url + current);
    }
    return total;
  }, []);
  return data;
};
const amountChange = async (item) => {
   item.totalPrice = parseFloat(
    (parseFloat(item.price) * parseFloat(item.amount)).toFixed(2)
  );
};
const addCart = (row, productType) => {
  row.buyLoad = true;
  api_post_addToCart([
    {
      amount: row.amount,
      bomItemsId: row.id,
      productType: productType,
    },
  ])
    .then((res) => {
      if ("0"==res.code) {
        row.existsCart="1";
      //  row.existsFavorites="1";
        ElMessage.success({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else if(("1"==res.code) ){
        row.existsCart="0";
      //  row.existsFavorites="0";
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else{
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }

    })
    .finally(() => {
      row.buyLoad = false;
    });
};

const addFavorit = (row, productType) => {
  row.favoritLoad = true;
  api_post_addToFavorites([
    {
      bomItemsId: row.id,
      productType: productType,
    },
  ])
    .then((res) => {
      if ("0"==res.code) {
      //  row.existsCart="1";
        row.existsFavorites="1";
        ElMessage.success({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else if(("1"==res.code) ){
      //  row.existsCart="0";
        row.existsFavorites="0";
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else{
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });

      }

    })
    .finally(() => {
      row.favoritLoad = false;
    });
};



// 使用 defineExpose 显式暴露 getTableList 方法 给 父组件
defineExpose({
  getTableList,
});
</script>
<style scoped lang="less">
.cart-list {
  color: #213547;
  font-size: 12px;

  .head-all-item {
    height: 39px;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border: 1px solid #e8e8e8;
    margin: 10px 0;
    &.small {
      height: 32px;
      font-size: 12px;
    }

    &-view {

      /* 新增一个加粗的类 */
      &.bold {
        font-weight: bold;
      }

      &.checkbox {
        width: 64px;
      }

      &.name {

        flex: 2;
        margin-right: 16px;
      }

      &.materierCode {
        flex: 2;
        margin-right: 16px;
      }

      &.quantity {
        flex: 2;
        margin-right: 16px;
      }

      &.unitPrice {
        flex: 3;
        margin-right: 16px;
      }

      &.remarks {
        flex: 3;
        margin-right: 16px;
      }

      &.operate {
        margin-right: 0;
        width: 150px;
      }
    }
  }

  .shopping-cart-item-all {
    border-radius: 8px;
    margin-bottom: 16px;

    .item-all-name {
      align-items: center;
      background-color: #f3f6f8;
      border: 1px solid #f0f3f5;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      display: flex;
      flex-direction: row;
      height: 48px;
      overflow: hidden;
      padding-left: 16px;
      color: #11192d;
      font-size: 14px;
      font-weight: 500;

      &.small {
        height: 32px;
        font-size: 12px;

        .truncate-text {
          display: inline-block;
          max-width: 177px;  /* 根据实际需要调整 */
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          text-decoration: underline;
          vertical-align: bottom;
          transition: all 0.3s ease;
        }

        .truncate-text:hover {
          max-width: none;
          overflow: visible;
          white-space: normal;
          word-break: break-all;
          background: #fff;
          position: relative;
          z-index: 1;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          padding: 4px 8px;
          border-radius: 4px;
        }
      }
    }

    .item-all-checkbox {
      margin-right: 16px;
    }

    .item-all-content {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-color: #f0f3f5;
      border-style: solid;
      border-width: 1px;
      position: relative;
      padding-left: 16px;
      word-break: break-all;

      .cart-all-item {
        align-items: flex-start;
        display: flex;
        position: relative;
        align-items: center;
        padding: 8px 0;
        cursor: pointer;

        &:hover {
          background-color: #f5f5f5;
        }

        .item-all-checkbox {
          width: 48px;
        }

        .cart-all-item-info {
          flex: 3;
          align-items: center;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          margin-right: 16px;
          padding-top: 2px;

          .cart-all-item-img {
            width: 46px;
            height: 46px;
            margin-right: 12px;
            background-color: rgba(0, 0, 0, 0.02);
            position: relative;
          }

          .cart-all-item-name {
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            color: #11192d;
            display: -webkit-box;
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            margin-bottom: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .cart-all-item-view {
          margin-right: 16px;

          &.materierCode {
            flex: 2;
          }

          &.quantity {
            flex: 2;
          }

          &.unitPrice {
            flex: 3;
            color: #ff5000;
            font-size: 18px;
            font-weight: 600;
          }

          &.remarks {
            flex: 3;
          }

          &.operate {
            margin-right: 0;
            width: 150px;

            .operate-item {
              display: block;
            }
          }
        }
      }
    }
  }
}

.wb-image {
  /deep/ .el-image-viewer__canvas {
    .el-image-viewer__img {
      background: #fff;
    }
  }
}
</style>
