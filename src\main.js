import { createApp } from 'vue'
import './style.css'
import './assets/style/wran_nav.css'
import './assets/style/header.css'
import './assets/style/bread_crumbs.css'
import './assets/style/layout.css'
import './assets/style/main.css'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

import App from './App.vue'
import router from './router/index'

// import './pages/detail/index'
// import './pages/detail/dragTable'
// import './pages/detail/dragSideBar'

const app = createApp(App)
const baseHost = '/sparts-wb'
//const baseHost = 'http://localhost:3004/sparts-wb'
//const base_img_url = baseHost+'/a/file/downFileByPath?filePath=_Files/'
//const base_downFileByPath_url = baseHost+'/a/file/downFileByPath?filePath='
// main.js 或任何其他的 Vue 组件文件中
// 获取当前页面的协议，主机名和端口

const base_img_url = '/fileviewer/_Files/'
const base_downFileByPath_url = '/fileviewer/'
//全局变量
app.provide('$base_host',baseHost)
export const  $base_host= baseHost //这种导出在js中使用 import {$base_host} from '../main.js';
app.provide('$base_img_url', base_img_url) //这种导出在vue文件中使用 const base_img_url = inject('$base_img_url')
app.provide('$base_downFileByPath_url',base_downFileByPath_url)


const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(pinia)
app.use(router)
app.use(ElementPlus)
app.mount('#app')
