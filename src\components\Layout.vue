<template>
  <header class="header">
    <div class="logo" @click="router.push('/home')">
      <img
        src="../assets/images/main/logo_turfone.png"
        style="height: 26px"
        alt="Logo"
      />
    </div>
    <nav class="navigation">
      <!--      <div class="header-item-wrap">
        <img
          src="../assets/images/main/menu.svg"
          @click="toggleDropdown('menuDropdown')"
        />
        <div
          class="header-item header_menu"
          id="header_menu"
          @click="toggleDropdown('menuDropdown')"
        >
          MENU
        </div>
      </div>-->

      <div class="header-item-wrap" @click="router.push('/home')">
        <img src="../assets/images/main/queryWite.svg" alt="" />
        <div
          class="header-item"
          :style="{ color: routName === 'Home' ? '#ccbfa5' : '#fff' }"
        >
          Home
        </div>
      </div>

      <div class="header-item-wrap" @click="toBrand">
        <img src="../assets/images/main/book.svg" alt="" />
        <div
          class="header-item"
          :style="{ color: routName === 'Catalog' ? '#ccbfa5' : '#fff' }"
        >
          Catalog
        </div>
      </div>

      <div class="header-item-wrap" @click="jump('favor')">
        <img src="../assets/images/main/favoritesWite.svg" alt="" />
        <div
          class="header-item"
          :style="{ color: routName === 'favor' ? '#ccbfa5' : '#fff' }"
        >
          Favorite
        </div>
      </div>

      <div class="header-item-wrap" @click="jump('warranty')">
        <img src="../assets/images/dropdown_menu/warrantyWite.svg" alt="" />
        <div
          class="header-item"
          :style="{ color: routName === 'Warranty' ? '#ccbfa5' : '#fff' }"
        >
          Warranty
        </div>
      </div>

      <el-badge :value="notice_noRead" :max="99" class="item" :offset="[-10, 0]">
        <div class="header-item-wrap" @click="jump('notice')">
          <img src="../assets/images/dropdown_menu/eventsWite.svg" alt="" />
          <div
              class="header-item"
              :style="{ color: routName === 'Notice' ? '#ccbfa5' : '#fff' }"
          >
            Notice
          </div>
        </div>
      </el-badge>

      <div class="header-item-wrap" style="margin-left: 10px;" @click="jump('order')">
        <img src="../assets/images/main/order.svg" alt="" />
        <div
            class="header-item"
            :style="{
            color:
              routName === 'OrderList' || routName == 'CreateOrder'
                ? '#ccbfa5'
                : '#fff',
          }"
        >
          Order
        </div>
      </div>

      <!-- <div class="header-item-wrap" @click="jump('cart')">
        <img src="../assets/images/main/cart.svg" alt="" />
        <div
            class="header-item"
            :style="{ color: routName === 'Cart' ? '#ccbfa5' : '#fff' }"
        >
          Cart
        </div>
      </div>-->

    </nav>
    <div class="user-info">
      <div class="header-item-wrap"
           style="height: 20px;line-height: 20px;color: #fff;text-align: center;"
           @click="jump('cart')">
        <img src="../assets/images/main/cartYellow.svg"
             style="vertical-align: middle; height: 20px;"
             alt=""
        />
        <div
            class="header-item"
            :style="{ color: routName === 'Cart' ? '#f2c802' : '#f29e02' }"
        >
           <span>Cart</span>
        </div>
      </div>

      <el-dropdown placement="bottom">
        <div
          style="
            height: 20px;
            line-height: 20px;
            color: #fff;
            text-align: center;
            display: flex; /* 使用 flex 布局 */
            align-items: center; /* 垂直居中对齐 */
            max-width: 150px; /* 设置最大宽度 */
            flex-shrink: 0; /* 防止内容被压缩 */
          "
        >
          <img
            style="vertical-align: middle; height: 20px"
            src="../assets/images/main/user.svg"
            alt=""
          />
          <span
              style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
            &nbsp;{{ userInfo.name }}
          </span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="dialogVisible = true"
              >Change Password</el-dropdown-item
            >
            <el-dropdown-item @click="profileEdit"
              >Profile</el-dropdown-item
            >
            <el-dropdown-item @click="loginOut">Log Out</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <!--    <div class="search-container">
      <input
        type="text"
        placeholder="Make a quick query here ..."
        @click="openSearch"
      />
      <button class="search-button">
        <img
          style="height: 20px"
          src="../assets/images/main/query.svg"
          alt=""
        />
      </button>
    </div>-->
    <!--  菜单栏下拉框保留 后续用  -->
    <div id="menuDropdown" class="dropdown-content">
      <div class="dropdown-item">
        <h3 class="dropdown-seclect">ORDERS</h3>
        <a href="#" @click="jump('order')">
          <img
            src="../assets/images/dropdown_menu/orders.svg"
            alt="Orders Icon"
          />
          MY PURCHASE ORDERS
        </a>
        <a href="#" @click="jump('cart')">
          <img
            src="../assets/images/dropdown_menu/shopping.svg"
            alt="Orders Icon"
          />
          SHOPPING CART
        </a>
        <a href="#" @click="jump('favor')">
          <img
            src="../assets/images/dropdown_menu/fav_fill.svg"
            alt="Orders Icon"
          />
          FAVORITES
        </a>
      </div>
      <div class="dropdown-item">
        <h3 class="dropdown-seclect">WARRANTY</h3>
        <a href="#">
          <img
            src="../assets/images/dropdown_menu/warranty.svg"
            alt="Orders Icon"
          />
          Warranty Registration
        </a>

        <h3 class="dropdown-seclect">MESSAGES</h3>
        <a href="#">
          <img
            src="../assets/images/dropdown_menu/events.svg"
            alt="Orders Icon"
          />
          NOTICE
        </a>
      </div>
      <div class="dropdown-item">
        <h3 @click="toBrand">
          <img
            src="../assets/images/dropdown_menu/home.svg"
            alt="Orders Icon"
          />
          HOME
        </h3>
        <!--        <h3>
          <img
            src="../assets/images/dropdown_menu/phone.svg"
            alt="Orders Icon"
          />
          CONTACTS
        </h3>-->
        <!--        <h3>
          <img
            src="../assets/images/dropdown_menu/MT_translate.svg"
            alt="Orders Icon"
          />
          中文
        </h3>-->
      </div>
    </div>
    <div v-click-outside="closeSearch">
      <SearchView v-show="showSearch" />
    </div>
  </header>

  <RouterView :key="route.fullPath" />
  <!--修改密码-->
  <el-dialog
    title="Change Password"
    v-model="dialogVisible"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      :model="passwordForm"
      :rules="rules"
      ref="passwordFormRef"
      label-width="100px"
    >
      <!-- <el-form-item label="旧密码" prop="oldPassword">
        <el-input
          v-model="passwordForm.oldPassword"
          type="password"
          autocomplete="off"
        />
      </el-form-item> -->

      <el-form-item label-width="150px" label="New Password" prop="newPassword">
        <el-input
          v-model="passwordForm.newPassword"
          type="password"
          autocomplete="off"
        />
      </el-form-item>

      <el-form-item
        label-width="150px"
        label="Confirm Password"
        prop="confirmPassword"
      >
        <el-input
          v-model="passwordForm.confirmPassword"
          type="password"
          autocomplete="off"
        />
      </el-form-item>
    </el-form>

    <!-- 弹窗底部按钮 -->
    <div slot="footer" style="text-align: right">
      <el-button @click="dialogVisible = false">Cancel</el-button>
      <el-button type="primary" @click="submitForm">Submit</el-button>
    </div>
  </el-dialog>
  <!--profile-->
  <el-dialog
    title="Profile"
    v-model="dialogVisible_profile"
    width="900px"
    style="margin-top: 50px"
    :before-close="handleClose_profile"
  >
    <div class="dealerInfor">
      <el-form :model="profileForm"  ref="profileFormRef">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item>
              <span class="label">*Dealer Name:</span>
              <el-input v-model="profileForm.dealerName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <span class="label"
                >Employer Identification Number (EIN If applicable):</span
              >
              <el-input v-model="profileForm.employerIdentificationNumber"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <span class="label">Doing Business As (DBA If applicable):</span>
              <el-input v-model="profileForm.doingBusinessAs"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <span class="label">Website:</span>
              <el-input v-model="profileForm.website"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item>
              <span class="label">*Business Street Address:</span>
              <el-input v-model="profileForm.street"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <span class="label">*City:</span>
              <el-input v-model="profileForm.city"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <span class="label">*State:</span>
              <el-input v-model="profileForm.state"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <span class="label">*Zip/Postal code:</span>
              <el-input v-model="profileForm.postalCode"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <span class="label">*Primary Contact Name:</span>
              <el-input v-model="profileForm.contactName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <span class="label">*Primary Contact Title:</span>
              <el-input v-model="profileForm.contactTittle"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <span class="label">*Primary Phone:</span>
              <el-input v-model="profileForm.phone"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <span class="label">*Primary E-mail:</span>
              <el-input v-model="profileForm.email"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item>
              <span class="label">*Posted Labor Shop Rate:</span>
              <el-input v-model="profileForm.laborShopRate"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 弹窗底部按钮 -->
    <div slot="footer" style="text-align: right">
      <el-button @click="dialogVisible_profile = false">Cancel</el-button>
      <el-button type="primary" @click="submitForm_profile">Submit</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { useRouter, useRoute } from "vue-router";
import {watch, ref, onMounted, onBeforeUnmount, computed, provide} from "vue";
import { useBreadcrumbStore } from "../stores/useBreadcrumbStore.js";
import SearchView from "./SearchView.vue";
import { ElMessage, ClickOutside as vClickOutside } from "element-plus";
import { api_post_logout2, api_post_modifyPwd,
  api_post_updateProfile,api_post_userInfoData,
  api_post_show_data,api_post_pmcNotice_noticeNumber

} from "../utils/api.js";
import { ElLoading } from "element-plus";

const router = useRouter();
const route = useRoute();
const routName = computed(() => {
  return route.name;
});

const notice_noRead = ref(0);
// 提供全局变量
provide('notice_noRead', notice_noRead);

const isHome = ref(false);
// 控制弹窗显示
const dialogVisible = ref(false);
// 控制弹窗显示
const dialogVisible_profile = ref(false);
// 表单数据
const passwordForm = ref({
  // oldPassword: "",
  newPassword: "",
  confirmPassword: "",
});
const passwordFormRef = ref(null);
//profile
const profileForm = ref({});
const profileFormRef = ref(null);

const userInfo = ref(
  JSON.parse(window.sessionStorage.getItem("userInfo")) || {}
);
watch(
  () => route.name,
  (route, _) => {
    isHome.value = route === "Home" ? true : false;
  },
  {
    immediate: true,
  }
);
console.log(`output->router`, route);
const breadcrumbStore = useBreadcrumbStore();

const toBrand = () => {
  router.push("/catalog");
  breadcrumbStore.clearBreadcrumbs();
};

let currentDropdown = null;

function toggleDropdown(id) {
  const dropdown = document.getElementById(id);
  const menuItem = document.getElementById("header_menu");

  if (dropdown.style.display === "flex") {
    dropdown.style.display = "none";
    menuItem.classList.remove("active");
    currentDropdown = null;
  } else {
    if (currentDropdown) {
      currentDropdown.style.display = "none";
      menuItem.classList.remove("active");
    }
    dropdown.style.display = "flex";
    menuItem.classList.add("active");
    currentDropdown = dropdown;
  }
}

function handleDocumentClick(event) {
  //在其它处点击将下拉框隐藏
  const dropdown = currentDropdown;
  const menuItem = document.getElementById("header_menu");
  if (
    dropdown &&
    !dropdown.contains(event.target) &&
    !menuItem.contains(event.target)
  ) {
    dropdown.style.display = "none";
    menuItem.classList.remove("active");
    currentDropdown = null;
  }
}

const jump = (type) => {
  switch (type) {
    case "cart":
      router.push("/cart?type=cart");
      break;
    case "favor":
      router.push("/favor?type=favor");
      break;
    case "order":
      router.push("/orderList");
      break;
    case "warranty":
      router.push("/warranty");
      break;
    case "notice":
      router.push("/notice");
      break;
    default:
      break;
  }
};

onMounted(() => {
  document.addEventListener("click", handleDocumentClick);
  /*查询通知数量*/
  api_post_pmcNotice_noticeNumber().then((res)=>{
    if(res){
        notice_noRead.value = res.length;
    }
  });

});

onBeforeUnmount(() => {
  document.removeEventListener("click", handleDocumentClick);
});

/** 检索下拉框相关 */
const showSearch = ref(false);

const openSearch = () => {
  showSearch.value = true;
};
const closeSearch = () => {
  showSearch.value = false;
};

const loginOut = () => {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(255, 255, 255, 0.7)",
  });
  api_post_logout2().then(() => {
    window.sessionStorage.removeItem("userInfo");
    loading.close();
    router.replace("/login");
  });
};

// 表单校验规则
const rules = {
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, message: "密码长度不能少于6个字符", trigger: "blur" },
  ],
  confirmPassword: [
    { required: true, message: "请再次输入新密码", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error("两次输入的密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

// 提交表单
const submitForm = () => {
  const formRef = passwordFormRef.value;
  formRef.validate((valid) => {
    if (valid) {
      api_post_modifyPwd({
        confirmPassword: passwordForm.value.confirmPassword,
        newPassword: passwordForm.value.newPassword,
      });
      ElMessage.success("Successfully!");
      // 在这里执行修改密码的逻辑
      console.log("password", passwordForm.value);
      dialogVisible.value = false;
    } else {
      console.log("表单验证失败");
      return false;
    }
  });
};

const profileEdit =() =>{

  dialogVisible_profile.value = true;
    api_post_show_data({...userInfo.value}).then((data) => {
      profileForm.value = data;
    });

}
// 提交表单profile
const submitForm_profile = () => {
  const formRef = profileFormRef.value;
   /*:rules="rules" formRef.validate暂未配置填写规则*/
  formRef.validate((valid) => {
    if (valid) {
       api_post_updateProfile(profileForm.value).then((res)=>{
         if(res){
              dialogVisible_profile.value = false;
             ElMessage.success("Successfully!");
            // 在这里执行修改密码的逻辑
            console.log("profile", profileForm.value);
         }
       });
    } else {
      console.log("表单验证失败");
      return false;
    }
  });
};

// 关闭弹窗时重置表单
const handleClose = () => {
  passwordForm.value = {
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  };
  passwordFormRef.value.resetFields();
  dialogVisible.value = false;
};

// 关闭弹窗时重置表单
const handleClose_profile = () => {
  dialogVisible_profile.value = false;
};
</script>

<style lang="less" scoped>
.fixed {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 101;
}
.user-info {
  cursor: pointer;
  display: flex; /* 设置为 flex 布局 */
  align-items: center; /* 垂直居中对齐 */
  justify-content: space-between; /* 子元素左右排列，中间留有空隙 */
  position: absolute;
  right: 10px;
  color: #fff;
  height: 12px;
  max-width: 200px; /* 设置最大宽度 */
}

.dealerInfor {
  padding: 2px;
  .el-form-item {
    margin-bottom: 10px;
  }
  .el-col {
    display: flex;
    align-items: center;
  }
  .el-col .el-input {
    flex-grow: 0.5;
  }
  .label {
    min-width: 200px;
    text-align: left;
    padding-right: 10px;
  }
}
</style>
