<template>
  <div class="order-container" v-loading="loading">
    <!-- 订单头信息 -->
    <div class="order-header">
      <h2>Order Number: {{ orderData.id }}</h2>
    </div>

    <!-- 订单明细表格 -->
    <el-table
        :data="tableData"
        style="width: 100%"
        border
        stripe
    >
      <el-table-column
          label="No."
          width="80"
          align="center"
      >
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
          prop="ename"
          label="Part Description"
          min-width="300"
      />
      <el-table-column
          prop="materielCode"
          label="Part Number"
          :width="isMobile ? 160 : 220"
          align="center"
      />
      <el-table-column
          prop="amount"
          label="Quantity"
          width="120"
          align="center"
      />
    </el-table>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useWindowSize } from '@vueuse/core'

import { api_post_queryShopOrderInfoView } from '../../utils/api'

const route = useRoute()
const { width } = useWindowSize()
const loading = ref(false)
const orderData = ref({})
const tableData = ref([])

const isMobile = computed(() => width.value <= 768)

// 获取订单详情
const fetchOrderDetail = async () => {
  try {
    loading.value = true
    const res = await api_post_queryShopOrderInfoView({
      orderId: route.query.id
    })

    if (res) {
      orderData.value = { ...res.orderData }
      tableData.value = res.tableData
    }
  } catch (error) {
    console.error('API Error:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  if (route.query.id) {
    fetchOrderDetail()
  }
})
</script>

<style scoped>
.order-container {
  padding: 20px 10px;
  width: 100%;
  box-sizing: border-box;
  margin: 0 auto;
}

.order-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #eee;
}

.order-header h2 {
  color: #303133;
  font-size: 24px;
  margin-bottom: 10px;
  word-break: break-all;
}

@media screen and (max-width: 768px) {
  .order-container {
    padding: 15px 8px;
  }

  .order-header h2 {
    font-size: 18px;
    margin-bottom: 8px;
  }

  ::v-deep .el-table {
    overflow-x: auto;
    display: block;
    -webkit-overflow-scrolling: touch;
  }

  ::v-deep .el-table__body-wrapper {
    overflow-x: auto;
  }

  ::v-deep .el-table th,
  ::v-deep .el-table td {
    padding: 8px 0;
    font-size: 12px;
  }

  ::v-deep .el-table-column--selection .cell {
    padding-left: 10px !important;
  }
}

::v-deep .el-table {
  font-size: 14px;
  box-shadow: none;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 20px;
}

::v-deep .el-table__header th {
  background: #f8f9fa;
  font-weight: 600;
}

.el-table::before {
  display: none;
}
</style>
