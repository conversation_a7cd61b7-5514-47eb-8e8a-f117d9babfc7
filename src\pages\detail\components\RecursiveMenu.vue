<template>
  <el-menu
    :default-active="default_active"
    class="el-menu-vertical-demo"
    :background-color="backgroundColor"
    :text-color="textColor"
    :default-openeds="default_active"
    :active-text-color="activeTextColor"
    @open="handleOpen"
    @close="handleClose"
  >
    <menu-item v-for="item in menuData" :key="item.id" :item="item" />
  </el-menu>
</template>

<script setup>
import MenuItem from './MenuItem.vue'
import { defineProps, defineEmits, onMounted, inject, ref } from 'vue'

const default_active = ref()

const props = defineProps({
  menuData: {
    type: Array,
    required: true,
  },
  defaultActive: {
    type: String,
    default: '',
  },
  backgroundColor: {
    type: String,
    default: 'transparent',
  },
  textColor: {
    type: String,
    default: '#000',
  },
  activeTextColor: {
    type: String,
    default: '#9B2423',
  },
  defaultOpeneds: {
    type: Array,
    default: () => [], // 传入要默认展开的菜单项 id 列表
  },
})

const emits = defineEmits(['open', 'close'])

const handleOpen = (index) => {
  emits('open', index)
}

const handleClose = (index) => {
  emits('close', index)
}

function removeBoms(str) {
  if (str.includes('/boms')) {
    str = str.replace('/boms', '').replace('.png', '.pdf')
    return str
  } else {
    return str
  }
}
// `provide` 和 `inject` 是一对API 父到子传参 不需要在每一个层级手动传递 props
const menuItemClick = inject('menuItemClick')

const findMenuItem = (menuItems, path) => {
  for (const item of menuItems) {
    if (item.fileName.includes('.pdf')) {
      if (removeBoms(path) === item.filePath) {
        return item
      }
    } else if (item.filePath === path) {
      return item
    }
    if (item.children && item.children.length) {
      const found = findMenuItem(item.children, path)
      if (found) {
        return found
      }
    }
  }
  return null
}

onMounted(() => {
  props.defaultOpeneds.forEach((openIndex) => {
    default_active.value = removeBoms(openIndex)
    console.log(`output->default_active.value`, default_active.value)
    const item = findMenuItem(props.menuData, openIndex)
    if (item && menuItemClick) {
      menuItemClick(item)
    }
  })
})
</script>
<style scoped>
.el-menu-vertical-demo {
  max-height: 87vh; /* 设置最大高度为视口高度，确保菜单不会超出屏幕 */
  overflow-y: auto; /* 当内容超出最大高度时，允许垂直滚动 */
}

</style>