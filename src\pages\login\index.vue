<template>
  <div class="login-container">
<!--    <div class="login-left">
      <img class="login-bg" src="/src/assets/images/login-bg.png" alt="" />
    </div>-->
    <div class="login-right">
      <div class="login-form">
        <img class="login-logo" src="/src/assets/images/main/logo_turfone.png" alt="" />
        <div class="login-subtitle">DEALER PORTAL</div>
        <div style="margin-bottom: 24px">
          <el-input
            v-model="userName"
            style="width: 100%"
            size="large"
            placeholder="User Name"
            :prefix-icon="User"
          />
        </div>
        <div>
          <el-input
            v-model="password"
            style="width: 100%"
            size="large"
            placeholder="Password"
            :prefix-icon="Lock"
            type="password"
          />
        </div>
        <el-button
          type="primary"
          style="width: 100%; margin-top: 24px"
          size="large"
          class="login-btn"
          @click="login"
          >Login</el-button
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from "vue";
import { useRoute, useRouter } from "vue-router";
import { User, Lock } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { api_post_login } from "../../utils/api";
const userName = ref("");
const password = ref("");

const router = useRouter();
onBeforeMount(() => {
  if (window.sessionStorage.getItem("userInfo")) {
    router.replace("/home");
  }
});
const login = async () => {
  if (!(password.value && userName.value)) {
    ElMessage.error("Please enter your account and password!");
    return;
  }
  const res = await api_post_login({
    password: password.value,
    mobile: userName.value,
  });
  if (res) {
    console.log("--userInfo--");
    console.log(res);
    console.log("--userInfo--");
    window.sessionStorage.setItem("userInfo", JSON.stringify(res));
   // ElMessage.success("Login successful");
    router.push("/home");
  } else {
    ElMessage.warning("Wrong Account Or Password");
  }
};
</script>

<style scoped lang="less">
.login-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  background-color: #f5f5f5;

  .login-left {
    flex: 1;
    background-color: #f9f9f9;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #8bd9f3;

    .login-bg {
      width: 65%;
      margin-top: -20%;
    }
  }

  .login-right {
    flex: 1;
    background-color: #f9f9f9;
    display: flex;
    justify-content: center;
    align-items: center;

    .login-form {
      text-align: center;
      width: 360px;
      margin-top: -15%;
    }

    .login-logo {
      width: 300px;
      margin: 0 auto 15px;
    }

    .login-subtitle {
      font-size: 16px;
      font-weight: 600;
      letter-spacing: 1px;
      color: #797373;
      margin: 0 0 20px;
    }

    .login-btn {
      background-color: #1677ff;
      border-color: #1677ff;
      font-weight: 600;
      font-size: 20px;
    }
  }
}
</style>
