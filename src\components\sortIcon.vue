<template>
  <div class="sort-warp">
    <el-icon
      style="cursor: pointer"
      size="12"
      :color="
        sortKey === currentKey && currentStatus === 'asc'
          ? 'rgb(64, 158, 255)'
          : 'rgb(144, 147, 153)'
      "
    >
      <CaretTop @click="setSort('asc')" />
    </el-icon>
    <el-icon
      style="cursor: pointer"
      size="12"
      :color="
        sortKey === currentKey && currentStatus === 'deasc'
          ? 'rgb(64, 158, 255)'
          : 'rgb(144, 147, 153)'
      "
    >
      <CaretBottom @click="setSort('deasc')" />
    </el-icon>
  </div>
</template>
<script setup>
import { ref, defineEmits, defineProps } from "vue";
import { CaretTop, CaretBottom } from "@element-plus/icons-vue";
const emits = defineEmits(["sort"]);

const props = defineProps(["sortKey", "currentKey"]);

const currentStatus = ref("");
const setSort = (state) => {
  currentStatus.value = state;
  emits("sort", {
    type: state,
    key: props.sortKey,
  });
};
</script>
<style lang="less" scoped>
.sort-warp {
  display: flex;
  flex-direction: column;
  margin-left: 4px;
}
</style>
