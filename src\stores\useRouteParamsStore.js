// store/useRouteParamsStore.js
import { defineStore } from 'pinia'

export const useRouteParamsStore = defineStore('routeParams', {
  state: () => ({
    params: {}, // 存储所有页面的路由参数
  }),
  actions: {
    // 设置单个参数
    setParam(key, value) {
      this.params[key] = value
    },
    // 批量设置参数
    setParams(newParams) {
      this.params = { ...this.params, ...newParams }
    },
    // 获取参数
    getParam(key) {
      return this.params[key]
    },
    // 清除单个参数
    clearParam(key) {
      delete this.params[key]
      
    },
    // 清除所有参数
    clearAllParams() {
      this.params = {}
    },
  },
  getters: {
    // 通过getter来获取特定参数
    getParamByKey: (state) => (key) => {
      return state.params[key]
    },
  },
})
