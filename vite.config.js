import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  server: {
    port: 3005,
    proxy: {
      "/sparts-wb": {
        //target: 'http://localhost:8080',
        target: "https://spareparts.weibang.com",

        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/sparts-wb/, "/sparts-wb"),
      },
      "/fileviewer": {
        target: "https://spareparts.weibang.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/fileviewer/, "/fileviewer"),
      },
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
  // build: {
  //   // Tell Vite how to handle assets
  //   assetsInlineLimit: 0, // Set to 0 to disable inlining of files
  //   rollupOptions: {
  //     // Add other Rollup options here
  //     output: {
  //       // Configure Rollup's output options
  //       //manual手册pdf预览页面用//const workerUrl = new URL('pdfjs-dist/build/pdf.worker.min.mjs', import.meta.url).href;
  //       assetFileNames: `assets/[name].[hash].[ext]`
  //     }
  //   }
  // },
});
