<template>
  <BreadCrumb
      :list="[
      {
        url: '/orderList',
        name: 'OrderList',
      },
    ]"
  />

  <div class="cart-content">
    <el-form :inline="true" :model="formInline" style="margin-top: 10px">
      <el-form-item label="Order Number">
        <el-input v-model="formInline.id" placeholder="Order No" />
      </el-form-item>
      <el-form-item label="Model/Part Name/Number">
        <el-input v-model="formInline.materielCode" placeholder="Model/Part Name/Number" />
      </el-form-item>
      <el-form-item label="Creation Date">
        <el-date-picker
            style="width: 255px;"
            v-model="formInline.createDate"
            type="daterange"
            range-separator="To"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">Search</el-button>
        <el-button type="danger" @click="onCreate">Generate New Order</el-button>
      </el-form-item>
    </el-form>

    <el-tabs
        v-model="activeName"
        class="demo-tabs"
        @tab-change="handleTabClick"
    >
      <el-tab-pane
          :label="item.label"
          :name="key"
          v-for="(item, key) of listUnion"
          :key="key"
          v-loading="loading"
      >
        <el-table :data="item.list" style="width: 100%" border stripe>
          <el-table-column label="Date" width="120" align="center">
            <template #default="scope">
              {{ dayjs(scope.row.createDate).format('MM/DD/YYYY') }}
            </template>
          </el-table-column>
          <el-table-column label="Order No." width="150" align="center">
            <template #default="scope">
              {{ scope.row.id }}
            </template>
          </el-table-column>
          <el-table-column label="Price" width="120" align="center">
            <template #default="scope">
              {{ userInfo.currency === '1' ? '¥' : userInfo.currency === '2' ? '$' : userInfo.currency === '3' ? '€' : '' }}
              {{ formatPrice(scope.row.lastMoney) }}
            </template>
          </el-table-column>
          <el-table-column label="Invoice" width="100" align="center">
            <template #default="scope">
              <el-button
                  @click="downloadPdf(scope.row)"
                  link
                  type="primary"
                  size="small"
                  style="font-size: 14px; font-weight: 300;"
              >
                Invoice
                <el-icon :size="14" style="margin-left: 4px"><Download /></el-icon>
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="Carrier & Tracking" align="center">
            <template #default="scope">
              <div
                  v-if="
                  scope.row.outWarehouseList && scope.row.outWarehouseList.length > 0
                "
              >
                <span
                    v-for="(trackingItem, index) in scope.row.outWarehouseList"
                    :key="index"
                >
                  CARRIER:
                  <span style="text-decoration: underline">{{
                      trackingItem.logisticsCompany
                    }}</span>
                  <span
                      style="
                      background-color: rgba(246, 249, 235, 0.5);
                      height: 100%;
                    "
                  >&nbsp;&nbsp;|&nbsp;&nbsp;</span
                  >
                  TRACKING:
                  <span class="truncate-text" :title="trackingItem.logisticsNumber" style="text-decoration: underline; cursor: pointer;">{{ trackingItem.logisticsNumber }}</span>
                  <br v-if="index < scope.row.outWarehouseList.length - 1" />
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="Status" width="150" align="center">
            <template #default="scope">
              <el-tag type="warning" v-if="scope.row.orderStatus === '1'"
              >To be confirmed</el-tag
              >
              <el-tag type="warning" v-if="scope.row.orderStatus === '20'"
              >Unfulfilled</el-tag
              >
              <el-tag type="danger" v-if="scope.row.orderStatus === '30'"
              >Waiting for pickup</el-tag
              >
              <el-tag type="danger" v-if="scope.row.orderStatus === '1000'"
              >Partially fulfilled</el-tag
              >
              <el-tag type="primary" v-if="scope.row.orderStatus === '1001'"
              >Fulfilled</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column label="Actions" width="280" align="center">
            <template #default="scope">
              <el-button
                  @click="downloadExcel(scope.row)"
                  link
                  type="primary"
                  size="small"
                  style="font-size: 14px; font-weight: 600; text-decoration: underline;"
              >
                <img style="width: 22px" src="../../assets/images/main/excel.svg" />
              </el-button>

              <el-popconfirm
                  title="Do you confirm the order?"
                  @confirm="confirmOrder(scope.row.id)"
                  v-if="scope.row.orderStatus === '1'"
              >
                <template #reference>
                  <el-button type="primary" size="small"> Confirm </el-button>
                </template>
              </el-popconfirm>

              <el-button
                  type="primary"
                  size="small"
                  @click="edit(scope.row)"
              >Details</el-button
              >

              <el-popconfirm
                  v-if="isWithin30Min(scope.row.createDate)"
                  title="Do you confirm delete?"
                  @confirm="deleteOrder(scope.row.id)"
              >
                <template #reference>
                  <el-button type="danger" size="small"> Delete Order </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <div style="display: flex; justify-content: flex-end; padding: 16px">
          <el-pagination
              @current-change="currentPageChange($event, item)"
              :current-page="item.pages.current"
              :page-size="10"
              :pager-count="11"
              layout="prev, pager, next"
              :total="item.pages.total"
          />
          <!-- pager-count默认7，分页器默认最多显示 7 个页码按钮 -->
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>

  <!-- 在orderList的template末尾添加 -->
  <el-dialog
      v-model="detailVisible"
      title="Order Info."
      width="85%"
      top="3vh"
      destroy-on-close
      :show-close="false"
  >
    <template #header="{ close }">
      <div class="dialog-header">
        <span>Order Details</span>
        <el-button
            type="danger"
            size="small"
            @click="close"
        >
          EXIT
        </el-button>
      </div>
    </template>
    <CreateOrder :params="currentOrder" />
  </el-dialog>
</template>
<script setup lang="ts">
import { Download } from '@element-plus/icons-vue'
import {ref, onMounted, inject} from "vue";
import BreadCrumb from "../../components/BreadCrumb.vue";
import CreateOrder from "../createOrder/index.vue";
import { useRoute, useRouter } from "vue-router";
import {
  api_post_shopOrderListPage,
  api_post_confirmOrder,
  api_post_deleteOrder,
  api_url_orderDetail_excel,
  api_url_generateInvoice_pdf,
  loadDown,downloadPDF,
  formatPrice
} from "../../utils/api";
import {dayjs, ElLoading, ElMessage} from "element-plus";
import axios from "axios";
const base_downFileByPath_url = inject("$base_downFileByPath_url");
const router = useRouter();
const activeName = ref("allOrders");
const checkAll = ref(false);
const loading = ref(false);
const userInfo = ref(
    JSON.parse(window.sessionStorage.getItem("userInfo")) || {}
);

const listUnion = ref({
  allOrders: {
    label: "All orders",
    orderStatus: undefined,
    pages: {
      current: 1,
      pageSize: 10,
      total: 100,
    },
    list: [],
    allchecked: [],
    checked: false,
  },
  Unfulfilled: {
    label: "Unfulfilled",
    orderStatus: "20",
    pages: {
      current: 1,
      total: 1,
    },
    list: [],
    allchecked: [],
    checked: false,
  },
  Waiting: {
    label: "Waiting for pickup",
    orderStatus: "30",
    pages: {
      current: 1,
      total: 1,
    },
    list: [],
    allchecked: [],
    checked: false,
  },
  Partially: {
    label: "Partially fulfilled",
    orderStatus: "1000",
    pages: {
      current: 1,
      total: 1,
    },
    list: [],
    allchecked: [],
    checked: false,
  },
  Completed:{
    label: "Fulfilled",
    orderStatus: "1001",
    pages: {
      current: 1,
      total: 1,
    },
    list: [],
    allchecked: [],
    checked: false,
  },

});
const formInline = ref({
  materielCode: "",
  id: "",
  createDate: [],
});

const isWithin30Min = (dateString) => {
  const currentDate = new Date();
  const givenDate = new Date(dateString);
  const timeDifference = currentDate - givenDate
  return timeDifference <= 0.5 * 60 * 60 * 1000;
}

const getTabData = () => {
  return listUnion.value[activeName.value];
};

const getList = () => {
  const {
    pages: { current },
    orderStatus,
  } = listUnion.value[activeName.value];
  loading.value = true;
  const [startTime, endTime] = formInline.value.createDate || [];
  const pramas = {
    id: formInline.value.id || "",
    materielCode: formInline.value.materielCode || "",
    // beginCreateDate: startTime ? dayjs(startTime).format("YYYY-MM-DD") : null,
    // endCreateDate: endTime ? dayjs(endTime).format("YYYY-MM-DD") : null,
    orderStatus: orderStatus || "",
    pageNo: current,
    pageSize: 10,
  };
  if (startTime && endTime) {
    pramas.beginCreateDate = dayjs(startTime).format("YYYY-MM-DD");
    pramas.endCreateDate = dayjs(endTime).format("YYYY-MM-DD");
  }
  api_post_shopOrderListPage(pramas)
      .then((result) => {
        listUnion.value[activeName.value].list = result.data;
        listUnion.value[activeName.value].pages.current = result.pageNo;
        listUnion.value[activeName.value].pages.total = result.total;
        console.log(result.data);
      })
      .finally(() => {
        loading.value = false;
      });
};

onMounted(() => {
  getList();
});

const onCreate = () => {
  router.push("/createOrder");
};

const onSearch = () => {
  getList();
};

const handleTabClick = (e) => {
  getList();
};

const setAllChecked = (val, item, array: []) => {
  if (val) {
    !array.includes(item.testId) && array.push(item.testId);
  } else {
    array.splice(array.indexOf(item.testId), 1);
  }
};

const handleCheckAllChange = (val: boolean, listItem) => {
  listItem.list.forEach((item) => {
    item.checked = val;
    setAllChecked(val, item, listItem.allchecked);
  });
  console.log(listItem.allchecked, listUnion.value);
};

const handleCheckItemAllChange = (e, list) => {
  list.children.forEach((el) => {
    el.checked = e;
  });
  checkAll.value = list.every((item) => item.checked);
};

const getImageUrl = (array, type) => {
  const data = array.reduce((total: string[], current: string) => {
    const urlArray = current.split("/").reverse();
    const isProduct = type !== "all" ? urlArray[0].includes("product") : true;
    if (
        urlArray.length > 0 &&
        isProduct &&
        (urlArray[0].includes(".jpg") || urlArray[0].includes(".png"))
    ) {
      total.push(base_downFileByPath_url+ current);
    }
    return total;
  }, []);
  return data;
};

const currentPageChange = (currentPage, item) => {
  item.pages.current = currentPage;
  getList();
};

const lastPage = () => {
  const current = getTabData();
  if (current.pages.current === 1) {
    --current.pages.current;
  }
  getList();
};

const deleteItem = () => {};

const nextPage = () => {
  const current = getTabData();
  console.log(current);
  ++current.pages.current;
  getList();
};

const deleteOrder = (id) => {
  api_post_deleteOrder(id)
      .then((res) => {
        console.log(res);
        getList();
      })
      .finally(() => {
        loading.value = false;
      });
};
const confirmOrder = async (id) => {
  api_post_confirmOrder(id)
      .then((res) => {
        console.log(res);
        getList();
      })
      .finally(() => {
        loading.value = false;
      });
};
/*const edit = (item) => {
  console.log(item);
  router.push("/createOrder?id=" + item.id);
};*/
const detailVisible = ref(false)
const currentOrder = ref({
  id:"",
});
const edit = (item) => {
  currentOrder.value.id = item.id
  detailVisible.value = true
}

const downloadExcel = (item) => {
  loadDown(api_url_orderDetail_excel + item.id);
};

const downloadPdf = async (item) => {
  try {
    const response = await axios.get(api_url_generateInvoice_pdf + item.id);
    if (response.data && response.data.msg) {
      const fileUrl = base_downFileByPath_url + response.data.msg;
      // 直接打开新窗口下载（不需要encodeURIComponent）
      window.open(fileUrl, '_blank');
    } else {
      ElMessage.error('Failed to get invoice!');
    }

  } catch (error) {
    ElMessage.error('Download failed: ' + error.message);
    console.error('PDF下载失败:', error);
  }
};

</script>
<style lang="less" scoped>
.cart-content {
  padding: 12px;

  .cart-control {
    display: flex;
    align-items: center;
    width: 100%;
    padding-bottom: 16px;
    padding-left: 16px;
  }


  .shopping-cart-item-all {
    border-radius: 8px;
    margin-bottom: 16px;

    &.head {
      .cart-all-item {
        height: auto;
        font-size: 12px;
      }
    }

    .item-all-name {
      align-items: center;
      background-color: #f0f9eb;
      border: 1px solid #f0f3f5;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      display: flex;
      flex-direction: row;
      overflow: hidden;
      color: #11192d;
      font-size: 12px;
      font-weight: 500;
      justify-content: space-between;
      padding: 2px 16px;

      &-content {
        display: flex;
        align-items: center;
      }

      &-btn {
      }
    }
    /*悬停颜色*/
    .item-all-name:hover {
      background-color: rgb(230, 230, 230);
    }

    .item-all-checkbox {
      margin-right: 16px;
    }

    .item-all-content {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-color: #f0f3f5;
      border-style: solid;
      border-width: 1px;
      position: relative;
      // padding-left: 16px;
      word-break: break-all;

      .cart-all-item {
        align-items: flex-start;
        display: flex;
        position: relative;
        align-items: center;
        padding: 8px;

        .item-all-checkbox {
          width: 48px;
        }

        .cart-all-item-info {
          flex: 3;
          align-items: center;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          margin-right: 16px;
          padding-top: 16px;

          .cart-all-item-img {
            width: 96px;
            height: 96px;
            margin-right: 12px;
            background-color: rgba(0, 0, 0, 0.02);
            position: relative;
          }

          .cart-all-item-name {
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            color: #11192d;
            display: -webkit-box;
            font-size: 12px;
            font-weight: 500;
            line-height: 20px;
            margin-bottom: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .cart-all-item-view {
          margin-right: 16px;
          font-size: 12px;

          &.materierCode {
            flex: 2;
          }

          &.quantity {
            flex: 1;
          }

          &.unitPrice {
            flex: 1;
            color: #ff5000;
            font-size: 18px;
            font-weight: 600;
          }

          &.remarks {
            flex: 3;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            margin-bottom: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          &.operate {
            margin-right: 0;
            width: 150px;

            .operate-item {
              display: block;
            }
          }
        }
      }
    }
  }

  .head-all-item {
    height: 39px;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border: 1px solid #e8e8e8;
    margin: 10px 0;
    padding-left: 16px;
    font-size: 12px;

    &-view {
      &.checkbox {
        width: 64px;
      }

      &.name {
        flex: 3;
        margin-right: 16px;
      }

      &.materierCode {
        flex: 2;
        margin-right: 16px;
      }

      &.quantity {
        flex: 1;
        margin-right: 16px;
      }

      &.unitPrice {
        flex: 1;
        margin-right: 16px;
      }

      &.remarks {
        flex: 3;
        margin-right: 16px;
      }

      &.operate {
        margin-right: 0;
        width: 150px;
      }
    }
  }

  .head-all-control {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding: 0 16px;

    > div {
      display: flex;
      align-items: center;
    }
  }
}

.wb-image{
  /deep/ .el-image-viewer__canvas{
    .el-image-viewer__img{
      background: #fff;
    }
  }
}

.truncate-text {
  display: inline-block;
  max-width: 185px;  /* 根据实际需要调整 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-decoration: underline;
  vertical-align: bottom;
  transition: all 0.3s ease;
}

.truncate-text:hover {
  max-width: none;
  overflow: visible;
  white-space: normal;
  word-break: break-all;
  background: #fff;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

:deep(.el-dialog__header) {
  padding: 0 !important; /* 移除默认内边距 */
  margin: 0;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px; /* 上下边距从20px减为8px */
  border-bottom: 1px solid #ebeef5;
  background: #f5f7fa;
}

.dialog-header span {
  font-size: 16px; /* 适当减小字体尺寸 */
  font-weight: 600;
  color: #303133;
}

/* 调整关闭按钮样式 */
.dialog-header .el-button {
  padding: 6px 12px; /* 缩小按钮尺寸 */
  border-radius: 4px;
  margin-right: -10px; /* 向右移动按钮位置 */
}

</style>