<template>
  <BreadCrumb />
  <el-tabs
    v-model="activeName"
    type="card"
    class="demo-tabs"
    @tab-click="handleClick"
    v-if="breadcrumb.length === 4"
  >
    <el-tab-pane label="Equipment" name="first">
      <div style="padding: 0px 16px" v-if="activeName === 'first'">
        <div
          v-if="tab_list.product[0]"
          style="background: #eee; padding: 16px; padding-left: 32px"
        >
          <div style="width: 500px; display: inline-block; text-align: right">
            <h2 style="flex: 1; margin-bottom: 5px">
              {{ tab_list.product[0].tittle }}
            </h2>

            <p style="margin-bottom: 5px">
              <!--                <span>{{ tab_list.product[0].materielName }}</span>
                <span> &nbsp;&nbsp;&nbsp;</span
                ><span>{{ tab_list.product[0].materielNameEn }}</span>
                <span> &nbsp;&nbsp;&nbsp;</span>-->
              <span>{{ tab_list.product[0].materielCode }}</span>
            </p>
            <div
              class="control-icon"
              style="color: darkgoldenrod; font-size: 22px"
            >
              <span>
                   <!-- 动态显示币种符号 -->
               {{ userInfo.currency === '1' ? '¥' : userInfo.currency === '2' ? '$' : userInfo.currency === '3' ? '€' : '' }}
                {{ formatPrice(tab_list.product[0].price) }}
              </span
              ><span> &nbsp;&nbsp;&nbsp;</span>
              <el-popover
                :visible="buyPopoverVisible"
                trigger="click"
                width="160px"
              >
                <template #reference>

                  <img  v-if="tab_list.product[0].existsCart == '1'"
                        @click="buyPopoverVisible = true"
                        src="../../assets/images/main/purCarYellow.svg"
                        alt="+"
                        style="height: 20px"
                  />
                  <img  v-else
                        @click="buyPopoverVisible = true"
                        src="../../assets/images/main/purCarYellowEmpty.svg"
                        alt="+"
                        style="height: 20px"
                  />

                </template>
                <template #default>
                  <!-- <div style="font-size: 12px;margin-bottom: 6px;">
                    Please enter the number of shopping
                  </div> -->
                  <el-input-number
                    v-model="buyNumber"
                    :min="1"
                    :max="9999"
                    size="small"
                    style="width: 100%;"
                  />
                  <div style="margin-top: 10px;text-align: right;">
                    <el-button size="small" @click="buyPopoverVisible = false"
                      >cancel</el-button
                    >
                    <el-button
                      @click.prevent="addCart(tab_list.product[0])"
                     type="primary"
                      size="small"
                    >
                      confirm
                    </el-button>
                  </div>
                </template>
              </el-popover>
              <img   v-if="tab_list.product[0].existsFavorites == '1'"
                     @click.prevent="addFavorit(tab_list.product[0])"
                     src="../../assets/images/main/favoritesYellow.svg"
                     alt="+"
                     style="height: 20px"
              />
              <img v-else  @click.prevent="addFavorit(tab_list.product[0])"
                   src="../../assets/images/main/favoritesYellowEmpty.svg"
                   alt="+"
                   style="height: 20px"
              />
              <!--<img
                    @click.prevent="addBuy()"
                    src="../../assets/images/main/buyItRed.svg"
                    alt="+"
                    style="height:20px;"
                />-->
            </div>
            <h4 style="margin: 8px 0"><!--{{ tab_list.product[0].description }}--></h4>

            <img
              style="width: 450px; margin: 3px 0"
              :src="`${base_downFileByPath_url}${carousel_list?.[0]?.filePath}`"
            />
            <!--   细节图轮播效果    暂时取消      -->
            <!--            <el-carousel
                            :interval="5000"
                            arrow="always"
                            type="card"
                            height="100px"
                        >
                          <el-carousel-item v-for="item in carousel_list_details">
                            <img
                                style="height: 100px;width: auto"
                                :src="`${base_downFileByPath_url}${item.filePath}`"
                            />
                          </el-carousel-item>
                        </el-carousel>-->
          </div>
          <img
            style="width: 500px; vertical-align: top; padding-left: 30px"
            :src="`${base_downFileByPath_url}${product_params_img.filePath}`"
          />
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane label="Spare Parts" name="second">
      <div class="product-list" style="margin-left: 24px">
        <div
          class="product-list-item"
          v-for="(item, index) in tab_list.boms"
          @click="toDetail(item)"
        >
          <div class="card-header">
            <text>{{ removeFileExtension(item.fileName) }}</text>
          </div>
          <div class="card-body">
            <img :src="`${base_downFileByPath_url}${item.filePath}`" />
          </div>
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane label="Video" name="third">
      <div class="product-list" style="margin-left: 24px">
        <div
          class="product-list-item"
          v-for="(item, index) in tab_list.video"
          @click="toDetail(item)"
        >
          <div class="card-header">
            <text>{{ removeFileExtension(item.fileName) }}</text>
          </div>
          <div class="card-body">
            <img src="/src/assets/images/video.jpg" />
          </div>
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane label="Manual" name="fourth">
      <div class="product-list" style="margin-left: 24px">
        <div
          class="product-list-item"
          v-for="(item, index) in tab_list.manual"
          @click="openPDF(item)"
        >
          <div class="card-header">
            <text>{{ removeFileExtension(item.fileName) }}</text>
          </div>
          <div class="card-body">
            <img src="/src/assets/images/pdf.jpg" />
          </div>
        </div>
      </div>
    </el-tab-pane>
  </el-tabs>
  <!--上方为bom的展示页面-->
  <!--下方为productType页面--model页面---->
  <div class="container" v-if="breadcrumb.length < 4">
    <div class="product-list">
      <div
        class="product-list-item"
        v-for="(item, index) in breadcrumbStore.products"
        @click="get_product(item)"
      >
        <div class="card-header">
          <text>{{ removeFileExtension(item.fileName) }}</text>
        </div>
        <div class="card-body">
          <img :src="`${base_img_url}${item.fileName}.png`" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { onMounted, ref, computed, reactive, inject } from "vue";
import {
  api_get_product,
  api_post_addToFavorites,
  api_post_addToCart,
  previewFile, api_post_listData_currentUserShopCar,
  formatPrice
} from "../../utils/api";
//useRoute 用于获取当前路由信息，而 useRouter 用于执行导航操作
import { useRoute, useRouter } from "vue-router";
import { ElLoading, ElMessage } from "element-plus";
import BreadCrumb from "../../components/BreadCrumb.vue";
import { useBreadcrumbStore } from "../../stores/useBreadcrumbStore.js";
import { useRouteParamsStore } from "../../stores/useRouteParamsStore.js";

const breadcrumbStore = useBreadcrumbStore();
const rpStore = useRouteParamsStore();

const breadcrumb = breadcrumbStore.breadcrumbs;

const route = useRoute();
const router = useRouter();
const product_list = ref([]);
const filetype = computed(() => breadcrumb.length);
const activeName = ref("first");
const text = ref("");
const base_img_url = inject("$base_img_url");
const base_downFileByPath_url = inject("$base_downFileByPath_url");
const currentItem = ref(null);
const carousel_list = ref([]);
const carousel_list_details = ref([]);
const product_params_img = ref();
/*const isDetail = ref(false)*/

const tab_list = reactive({
  boms: [],
  product: [],
  manual: [],
  video: [],
});

const userInfo = ref(
    JSON.parse(window.sessionStorage.getItem("userInfo")) || {}
);

const buyPopoverVisible = ref(false);

const buyNumber = ref(1);

function removeFileExtension(filename) {
  const lastDotIndex = filename.lastIndexOf(".");
  if (lastDotIndex === -1) {
    // 没有找到点，表示没有扩展名
    return filename;
  }
  return filename.substring(0, lastDotIndex);
}
const getNextType = (type) => {
  switch (type) {
    case "brand":
      return "productType";
      break;
    case "productType":
      return "model";
      break;
    case "model":
      return "bom";
      break;
    default:
      break;
  }
};

const openPDF = (item) => {
  console.log(item.filePath);
  window.open("/fileviewer" + item.filePath, "_blank");
};

const toDetail = (item) => {
  router.push({
    path: "/detail",
    query: {
      params: item.filePath,
    },
  });
  //用于面包屑跳转到 path: '/detail',
  //用于面包屑跳转到 path: '/detail',
  breadcrumbStore.addBreadcrumb({
    id: item.id,
    fileName: removeFileExtension(item.fileName),
    filePath: item.filePath,
    type: "toDetail",
  });
};

const getText = async () => {
  //读取txt文件舍弃
  const paths = tab_list.product.filter((item) =>
    item.fileName.includes("txt")
  );
  const path = paths.length > 0 ? paths[0].filePath : "";
  const result = await fetch(`${base_downFileByPath_url}${path}`, {}).then(
    (res) => res.text()
  );
  text.value = result;
};

//brand--productType--model--"bom
const get_product = async (item) => {
  //每次进来都是整机
  activeName.value = "first";
  //onMounted中初始化
  //productType--model--"bom
  //home/<USER>
  currentItem.value = item;
  if (item.type === "bom") {
    //===此为===bom处理
    const resp = await api_get_product({
      id: item.id,
    });

    console.log("Start_____bom页面初始化_____");

    tab_list.boms = resp.filter(
      (item) => item.filePath.includes("boms") && item.fileName !== "boms"
    );

    tab_list.manual = resp.filter(
      (item) =>
        item.filePath.includes("manual") && !item.filePath.endsWith("manual")
    );

    tab_list.video = resp.filter((item) => item.filePath.includes("mp4"));
    //getText();

    tab_list.product = resp.filter((item) => item.filePath.endsWith("product"));
    console.log("product_____bom页面初始化_____", tab_list.product[0]);
    carousel_list.value = resp
      .filter((item) => item.filePath.includes("/product/product"))
      .sort((a, b) => a.filePath.localeCompare(b.filePath));

    carousel_list_details.value = resp.filter((item) =>
      item.filePath.includes("/product/detail")
    );

    product_params_img.value = resp.filter((item) =>
      item.filePath.includes("/product/parameter")
    )[0];

    //1购物车  2收藏夹
    const currentUserShopCar = await api_post_listData_currentUserShopCar()
    if (currentUserShopCar.some(cart1 => cart1.type == "1" && cart1.bomItemsId === tab_list.product[0].id)) {
      tab_list.product[0].existsCart = '1';
    }
    if (currentUserShopCar.some(cart1 => cart1.type == "2" && cart1.bomItemsId === tab_list.product[0].id)) {
      tab_list.product[0].existsFavorites="1";
    }

    console.log("End_____bom页面初始化_____");
  } else {
    debugger;
    const resp = await api_get_product({
      id: item.id,
      type: getNextType(item.type),
    });
    breadcrumbStore.setProducts(resp);
  }

  breadcrumbStore.addBreadcrumb({
    id: item.id,
    fileName: item.fileName,
    filePath: item.filePath,
    type: getNextType(item.type),
  });
};
const addCart = (product) => {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(255, 255, 255, 0.7)",
  });
  api_post_addToCart([
    {
      amount: buyNumber.value || 1,
      bomItemsId: tab_list.product[0].id,
      productType: "product",
    },
  ])
    .then((res) => {

      if ("0"==res.code) {
        product.existsCart="1";
        // product.existsFavorites="1";
        ElMessage.success({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else if(("1"==res.code) ){
        product.existsCart="0";
        // product.existsFavorites="0";
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else{
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }

      buyPopoverVisible.value = false;

    })
    .finally(() => {
      loading.close();
    });
};

const addFavorit = (product) => {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(255, 255, 255, 0.7)",
  });
  api_post_addToFavorites([
    {
      bomItemsId: tab_list.product[0].id,
      productType: "product",
    },
  ])
    .then((res) => {

      if ("0"==res.code) {
        //product.existsCart="1";
        product.existsFavorites="1";
        ElMessage.success({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else if(("1"==res.code) ){
        //product.existsCart="0";
        product.existsFavorites="0";
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else{
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }


    })
    .finally(() => {
      loading.close();
    });
};
const addBuy = () => {
  const selectedList = [
    {
      ...tab_list.product[0],
      productFileList: [
        ...carousel_list.value,
        ...carousel_list_details.value,
      ].map((item) => item.filePath),
      cartList: [
        {
          ...tab_list.product[0],
          quantity: 1,
          amount: 1,
          id: tab_list.product[0].id,
          productType: "product",
        },
      ],
    },
  ];
  console.log(selectedList);
  rpStore.setParam("selectedList", selectedList);
  router.push("/createOrder");
};

onMounted(() => {
  if (!rpStore.getParamByKey("id")) {
    router.go(-1);
    return;
  }
  const id = rpStore.getParamByKey("id");
  const fileName = rpStore.getParamByKey("fileName");
  const filePath = rpStore.getParamByKey("filePath");
  const type = rpStore.getParamByKey("type");
  // isDetail.value = rpStore.getParamByKey('detail') ? true : false

  get_product({
    id,
    fileName,
    filePath,
    type,
  });
});
</script>

<style lang="less" scoped>
.product-list {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  width: 100%;

  .product-list-item {
    display: flex;
    flex-direction: column;

    .card-header {
      padding: 4px 0 24px 12px;
      width: 100%;
      max-height: 50px;
      background-color: #ececec;
      display: flex;
      flex-direction: column;

      text:nth-child(1) {
        font-size: 14px;
        font-weight: bold;
        color: #5f666a;
      }
    }

    .card-body {
      padding: 12px;
      //   min-height: 150px;
      border: 2px solid #ececec;

      img {
        height: 160px;
        /*width: 100%;*/
      }
    }
  }
}

::v-deep {
  // .el-tabs__item {
  //   // background-color: #e8e8e8;
  // }

  // .el-carousel,
  // .el-carousel__container {
  //   height: 400px !important;
  // }
}

.control-icon {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 12px;
  > img {
    width: 28px;
    height: auto;
    margin-left: 16px;
    cursor: pointer;
  }
}
</style>
