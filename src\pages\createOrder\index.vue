<template>
  <BreadCrumb
      v-if="!currentOrderId"
      :list="[
      {
        url: breadcrumbUrl,
        name: 'OrderInfo.',
      },
    ]"
  />
  <div style="padding: 0 16px" v-loading="loading">
    <div style="margin: 16px 0">
      <el-form :inline="true" :model="formInline" class="demo-form-inline">

        <el-row :gutter="6"  v-if="userInfo.userType == 1 &&  !currentOrderId">
              <el-col :span="6"  >
                <el-form-item label="Dealer Account"
                              style="width: 100%"
                              label-width="120px">
                  <el-select
                      placeholder="Dealer Account"
                      v-model="formInline.salesperson"
                      style="width: 100%"
                      clearable
                      filterable
                      @change="dealerAccountChange"
                  >
                    <el-option
                        v-for="item in dealerAccountData"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6" >
                <el-form-item label="Dealer Company"
                              style="width: 100%"
                              label-width="120px">
                    <el-select
                        placeholder="Dealer Company"
                        v-model="formInline.customer"
                        style="width: 100%"
                        disabled
                        clearable
                        filterable
                        @change=""
                    >
                      <el-option
                          v-for="item in dealerCompanytData"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                      />
                    </el-select>
                  </el-form-item>

              </el-col>
              <el-col :span="12"
                      style="display: flex; justify-content: flex-end; gap: 10px">
                      <el-popconfirm v-if="isEdit"
                                     title="Confirm order submission?"
                                     @confirm="save">
                        <template #reference>
                          <el-button type="primary">Save</el-button>
                        </template>
                      </el-popconfirm>
<!--                      <el-button v-if="!isEdit"
                                 type="danger"
                                 @click="exitPage">EXIT</el-button>-->
              </el-col>

        </el-row>

        <el-row :gutter="6">
          <el-col :span="6" v-if="formInline.id">
            <el-form-item label="Order Num"
                          style="width: 100%"
                          label-width="120px">
              <el-input
                disabled
                v-model="formInline.id"
                placeholder="Order Number"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="Creation Time"
                          style="width: 100%"
                          label-width="120px">
              <el-date-picker
                value-format="YYYY-MM-DD"
                disabled
                v-model="formInline.createDate"
                type="date"
                placeholder="Choose"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="Delivery Time"
                          style="width: 100%"
                          label-width="120px">
              <el-date-picker
                :disabled="!isEdit"
                value-format="YYYY-MM-DD"
                v-model="formInline.deliveryTime"
                :clearable="false"
                type="date"
                placeholder="Choose"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col  v-if="userInfo.userType == 2"
                   :span="formInline.id ? 6 : 12"
                    style="display: flex; justify-content: flex-end; gap: 10px">
            <el-popconfirm v-if="isEdit"
                           title="Confirm order submission?"
                           @confirm="save">
              <template #reference>
                <el-button type="primary">Save</el-button>
              </template>
            </el-popconfirm>
<!--            <el-button v-if="!isEdit"
                       type="danger"
                       @click="exitPage">EXIT</el-button>-->
          </el-col>
        </el-row>

        <el-row :gutter="6">
          <el-col :span="6">
            <el-form-item
                label="Contact Name"
                style="width: 100%"
                label-width="120px"
            >
              <el-input
                  :disabled="!isEdit"
                  v-model="formInline.contactName"
                  placeholder="Please enter"
                  :clearable="false"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
                label="Contact Phone"
                style="width: 100%"
                label-width="120px"
            >
              <el-input
                  :disabled="!isEdit"
                  v-model="formInline.contactPhone"
                  placeholder="Please enter"
                  clearable
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
                label="Contact E-mail"
                style="width: 100%"
                label-width="120px"
            >
              <el-input
                  :disabled="!isEdit"
                  v-model="formInline.contactEmail"
                  placeholder="Please enter"
                  clearable
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="6">
          <el-col :span="6">
            <el-form-item
                label="Delivery Address"
                style="width: 100%"
                label-width="120px"
            >
              <el-input
                  type="textarea"
                  :disabled="!isEdit"
                  v-model="formInline.collectAddress"
                  placeholder="Please enter"
                  clearable
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
                label="Billing Address"
                style="width: 100%"
                label-width="120px"
            >
              <el-input
                  type="textarea"
                  :disabled="!isEdit"
                  v-model="formInline.billingAddress"
                  placeholder="Please enter"
                  clearable
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
                label="Payment"
                style="width: 100%"
                label-width="120px"
            >
              <el-date-picker
                  :disabled="!isEdit"
                  value-format="YYYY-MM-DD"
                  v-model="formInline.payment"
                  :clearable="false"
                  type="date"
                  placeholder="Choose"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="6">
          <el-col :span="18">
            <el-form-item
                label="Remark"
                style="width: 100%"
                label-width="120px"
            >
              <el-input
                  :disabled="!isEdit"
                  v-model="formInline.remarks"
                  placeholder="Please enter"
                  clearable
                  style="width: 100%"
                  :rows="1"
                  type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <div>
          <div style="display: flex">
    <!--  <el-button
              type="danger"
              style="margin-bottom: 16px"
              @click="openDialog"
              v-if="isEdit"
              >Add Parts</el-button
            >-->
            <el-button
                v-if="isEdit"
                @click="downloadExcelTemplate"
                link
                type="primary"
                size="small"
                style="font-size: 14px; font-weight: 600;text-decoration: underline;"
            >
              <img
                  style="width: 23px"
                  src="../../assets/images/main/excel.svg"
              />
              Import Template
            </el-button>
            &nbsp;
            <el-upload
              v-if="isEdit"
              action="/sparts-wb/a/pmc/shopOrder/orderImport"
              ref="uploadRef"
              class="upload-demo"
              style="margin-left: 10px"
              :show-file-list="false"
              accept=".xlsx,.xls"
              :on-success="onSuccess"
              :before-upload="beforeUpload"
            >
              <template #trigger>
                <el-button type="danger" size="default" @click="">Excel Import</el-button>
              </template>
            </el-upload>


          </div>

          <div class="cart-list">
            <div class="head-all-item">
              <div
                class="item-all-image"
                style="width: 186px; padding-left: 16px"
              >
                Equipment/Fitted On
              </div>
              <!--<div class="head-all-item-view name">Name</div>-->
              <div
                class="head-all-item-view"
                style="flex: 1; margin-right: 16px"
              >
                Index
              </div>
              <div class="head-all-item-view name">
                Description
                <SortIcon
                  @sort="onSort($event, 'materielNameEn')"
                  sortKey="materielNameEn"
                  :currentKey="currentSortKey"
                />
              </div>
              <div class="head-all-item-view materierCode">
                Part Number
                <SortIcon
                  @sort="onSort($event, 'materielCode')"
                  sortKey="materielCode"
                  :currentKey="currentSortKey"
                />
              </div>
          <!--<div class="head-all-item-view quantity" style="flex: 1">
                Quantity
              </div>-->
              <div class="head-all-item-view unitPrice" style="flex: 2">
                Price/Unit
              </div>
              <div class="head-all-item-view amount" style="flex: 3">
                Quantity
              </div>
              <div class="head-all-item-view amount" style="flex: 2">
                Total
              </div>
              <div class="head-all-item-view remarks">Remark</div>
              <div class="head-all-item-view operate"></div>
              <!-- <div class="head-all-item-view operate"></div>-->
            </div>
            <div
              class="shopping-cart-item-all"
              v-for="(commodityItem, itemIndex) in tableData"
            >
              <div class="item-all-name small">
                <!--    成品信息            -->
                {{ commodityItem.materielCode }}
                <!--&nbsp;&nbsp;&nbsp;{{ commodityItem.materielName }}-->
                &nbsp;&nbsp;&nbsp;{{ commodityItem.materielNameEn }}
              </div>
              <div style="display: flex; align-items: flex-start">
                <div
                  style="min-width: 170px; max-width: 170px; padding: 0 10px"
                >
                  <el-image
                    class="wb-image"
                    :src="
                      getImageUrl(commodityItem.productFileList || [])[0] || ''
                    "
                    :preview-src-list="
                      getImageUrl(commodityItem.productFileList || [], 'all')
                    "
                    style="width: 100%; height: auto"
                    :zoom-rate="1.2"
                    :max-scale="7"
                    :min-scale="0.2"
                    :initial-index="4"
                    fit="cover"
                  />
                </div>
                <div
                  class="item-all-content"
                  style="flex: 1; min-height: 160px"
                >
                  <div
                    class="cart-all-item"
                    v-for="(item, index) in commodityItem.cartList || []"
                  >
                    <!--<div class="cart-all-item-info">
                      <div
                        class="cart-all-item-name"
                        :title="item.materielName"
                      >
                        {{ item.materielName }}
                      </div>
                    </div>-->
                    <div class="cart-all-item-view" style="flex: 1">
                      {{ getTableIndex(itemIndex, index) }}
                    </div>
                    <div class="cart-all-item-info">
                      <div
                        class="cart-all-item-name"
                        :title="item.materielNameEn"
                      >
                        <el-button  v-if="item.bomId && item.filePath && item.indexNo"
                                    @click="jumpDetail(item)"
                                    link
                                    type="primary"
                                    size="small"
                                    style="font-size: 12px; font-weight: 600;text-decoration: underline;"
                        >
                          <img src="../../assets/images/main/find.svg" alt="+" height="17px" />
                          <!-- Expl.View-->
                        </el-button>
                        {{ item.materielNameEn }}
                      </div>
                    </div>
                    <div class="cart-all-item-view materierCode">
                      {{ item.materielCode }}
                    </div>
               <!-- <div class="cart-all-item-view amount">
                      {{ item.quantity }}
                    </div>-->
                    <div class="cart-all-item-view unitPrice" style="flex: 2">
                      <!-- 动态显示币种符号 -->
                      {{ userInfo.currency === '1' ? '¥' : userInfo.currency === '2' ? '$' : userInfo.currency === '3' ? '€' : '' }}
                      {{ formatPrice(item.unitPrice || item.price) }}
                    </div>
                    <div class="cart-all-item-view quantity">
                      <el-input-number
                        v-model="item.amount"
                        :min="1"
                        :disabled="!isEdit"
                      />
                    </div>
                    <div class="cart-all-item-view unitPrice" style="flex: 2">
                      <!-- 动态显示币种符号 -->
                      {{ userInfo.currency === '1' ? '¥' : userInfo.currency === '2' ? '$' : userInfo.currency === '3' ? '€' : '' }}
                      {{
                        formatPrice(
                              multiply(
                                Number(item.unitPrice || item.price || 0),
                                item.amount || 1
                              )
                         )
                      }}
                    </div>
                    <div class="cart-all-item-view remarks">
                      <el-input
                        :disabled="!isEdit"
                        v-model="item.remarks"
                        :min="1"
                        :autosize="{
                          minRows: 2,
                          maxRows: 4,
                        }"
                        type="textarea"
                        placeholder="Please enter a comment"
                      />
                    </div>

<!--                    <div class="cart-all-item-view operate">
                      <el-button  v-if="item.bomId && item.filePath && item.indexNo"
                                  @click="jumpDetail(item)"
                                  link
                                  type="primary"
                                  size="small"
                                  style="font-size: 12px; font-weight: 600;text-decoration: underline;"
                      >
                        <img src="../../assets/images/main/find.svg" alt="+" height="18px" />
                        &lt;!&ndash; Expl.View&ndash;&gt;
                      </el-button>
                    </div>-->

                    <div class="cart-all-item-view operate">
                      <el-popconfirm
                        :disabled="!isEdit"
                        title="Are you sure you delete?"
                        @confirm="
                          deleteTableItem({ commodityItem, item, index })
                        "
                      >
                        <template #reference>
                          <el-link
                            :disabled="!isEdit"
                            type="primary"
                            class="operate-item"
                            :underline="false"
                            >Delete</el-link
                          >
                        </template>
                      </el-popconfirm>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-if="tableData.length <= 0"
              style="text-align: center; margin-top: 20px; color: #a8abb2"
            >
              No data
            </div>
          </div>

        </div>
      </el-form>
    </div>
    <!-- Add By Part Code 弹窗   -->
    <el-dialog
      v-model="dialogFormVisible"
      title="Select materials"
      width="70%"
      top="1vh"
      :show-close="false"
      :before-close="handleModalClose"
    >
      <template #header="{ close, titleId, titleClass }">
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
          "
        >
          <div style="display: flex; align-items: center">
            Part Name/Number:
            <el-input
              v-model="dialogSearch.name"
              placeholder="Part Name/Number"
              clearable
              style="width: 200px; margin: 0 10px"
            />
            <el-button
              @click="searchTableData"
              type="primary"
              :loading="modaLoading"
              >Query</el-button
            >
          </div>
          <div style="font-size: 24px; color: #909399; cursor: pointer">
            <el-icon style="margin-right: 10px" @click="confirmHandler">
              <Check />
            </el-icon>
            <el-icon @click="close">
              <Close />
            </el-icon>
          </div>
        </div>
      </template>
      <div v-loading="modaLoading">
        <div style="font-size: 12px; color: #ccc">
          The Parts that can be selected:
        </div>
        <div class="cart-list">
          <div class="head-all-item small">
            <div
              class="item-all-image"
              style="width: 140px; padding-left: 16px"
            >
              From The Machine
            </div>
            <div
              style="
                display: flex;
                align-items: center;
                padding-left: 16px;
                flex: 1;
              "
            >
              <div class="head-all-item-view materierCode">Part Number</div>
<!--              <div class="head-all-item-view quantity">Name_Chinese</div>-->
              <div class="head-all-item-view quantity">Description</div>
              <div class="head-all-item-view quantity">Price</div>
              <div class="head-all-item-view operate">Operate</div>
            </div>
          </div>
          <el-scrollbar ref="scrollbarRef" height="150px" always>
            <div>
              <div
                class="shopping-cart-item-all"
                v-for="(commodityItem, index) in dialogAllTable"
              >
                <div
                  class="item-all-name small"
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding-right: 88px;
                  "
                >
                  {{ commodityItem.materielCode }}
                  <el-icon
                    color="#337ecc"
                    @click="
                      addMaterial({
                        commodityItem,
                        item: {
                          ...commodityItem,
                          quantity: commodityItem.amount || 0,
                          cartList: [],
                          productType: 'product',
                        },
                        index,
                      })
                    "
                    style="cursor: pointer; font-size: 14px"
                  >
                    <Plus />
                  </el-icon>
                </div>
                <div style="display: flex; align-items: flex-start">
                  <div
                    style="min-width: 140px; max-width: 140px; padding: 0 10px"
                  >
                    <el-image
                      class="wb-image"
                      :src="
                        getImageUrl(commodityItem.productFileList || [])[0] ||
                        ''
                      "
                      :preview-src-list="
                        getImageUrl(commodityItem.productFileList || [], 'all')
                      "
                      style="width: 100%; height: auto"
                      :zoom-rate="1.2"
                      :max-scale="7"
                      :min-scale="0.2"
                      :initial-index="4"
                      fit="cover"
                    />
                  </div>
                  <div
                    class="item-all-content"
                    style="flex: 1; min-height: 130px"
                  >
                    <div
                      class="cart-all-item"
                      v-for="(item, index) in commodityItem.spartList || []"
                    >
                      <div class="cart-all-item-view materierCode">
                        {{ item.materielCode }}
                      </div>
                <!-- <div class="cart-all-item-view quantity">
                        {{ item.materielName }}
                      </div>-->
                      <div class="cart-all-item-view quantity">
                        {{ item.materielNameEn }}
                      </div>
                      <div class="cart-all-item-view quantity">
                        {{ item.price }}
                      </div>
                      <div class="cart-all-item-view operate">
                        <el-icon
                          color="#337ecc"
                          @click="
                            addMaterial({
                              commodityItem,
                              item: {
                                ...item,
                                quantity: item.amount || 0,
                                productType: 'part',
                              },
                              index,
                            })
                          "
                          style="cursor: pointer"
                        >
                          <Plus />
                        </el-icon>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="dialogAllTable.length <= 0"
                style="text-align: center; margin-top: 20px; color: #a8abb2"
              >
                No Data
              </div>
            </div>
          </el-scrollbar>
        </div>
        <div style="margin-top: 16px">
          <div style="font-size: 12px; color: #ccc">
            Materials that have been selected:
          </div>
          <div class="cart-list">
            <div class="head-all-item small">
              <div
                class="item-all-image"
                style="width: 140px; padding-left: 16px"
              >
                From The Machine
              </div>
              <div
                style="
                  display: flex;
                  align-items: center;
                  padding-left: 16px;
                  flex: 1;
                "
              >
                <div class="head-all-item-view materierCode">Part Number</div>
          <!-- <div class="head-all-item-view quantity">Name_Chinese</div>-->
                <div class="head-all-item-view quantity">Description</div>
                <div class="head-all-item-view quantity">Price</div>
                <div class="head-all-item-view operate">Operate</div>
              </div>
            </div>
            <el-scrollbar ref="scrollbarRef" height="300px" always>
              <div>
                <div
                  class="shopping-cart-item-all"
                  v-for="(commodityItem, index) in dialogSelectTable"
                >
                  <div class="item-all-name small">
                    {{ commodityItem.materielCode }}
                  </div>
                  <div style="display: flex; align-items: flex-start">
                    <div
                      style="
                        min-width: 140px;
                        max-width: 140px;
                        padding: 0 10px;
                      "
                    >
                      <el-image
                        class="wb-image"
                        :src="
                          getImageUrl(commodityItem.productFileList || [])[0] ||
                          ''
                        "
                        :preview-src-list="
                          getImageUrl(
                            commodityItem.productFileList || [],
                            'all'
                          )
                        "
                        style="width: 100%; height: auto"
                        :zoom-rate="1.2"
                        :max-scale="7"
                        :min-scale="0.2"
                        :initial-index="4"
                        fit="cover"
                      />
                    </div>
                    <div
                      class="item-all-content"
                      style="flex: 1; min-height: 130px"
                    >
                      <div
                        class="cart-all-item"
                        v-for="(item, index) in commodityItem.spartList || []"
                      >
                        <div class="cart-all-item-view materierCode">
                          {{ item.materielCode }}
                        </div>
                <!--    <div class="cart-all-item-view quantity">
                          {{ item.materielName }}
                        </div>-->
                        <div class="cart-all-item-view quantity">
                          {{ item.materielNameEn }}
                        </div>
                        <div class="cart-all-item-view quantity">
                          {{ item.price }}
                        </div>
                        <div class="cart-all-item-view operate">
                          <el-icon color="#337ecc" style="cursor: pointer">
                            <Delete
                              @click="
                                deleteMaterial({
                                  commodityItem,
                                  item,
                                  index,
                                })
                              "
                            />
                          </el-icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  v-if="dialogSelectTable.length <= 0"
                  style="text-align: center; margin-top: 20px; color: #a8abb2"
                >
                  No Data
                </div>
              </div>
            </el-scrollbar>
          </div>

        </div>
      </div>
    </el-dialog>

    <!-- Diagram弹窗--BomCanvas   -->
    <el-dialog
        destroy-on-close
        v-model="dialogBomCanvasVisible"
        title="BOM Diagram"
        width="70%"
        top="1vh"
        :show-close="true"
        :before-close="handleBomCanvasModalClose"
    >
      <BomCanvas :params="paramsBomCanvas" ref="bomCanvasRef" />

    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import {ref, onMounted, inject, computed, defineProps} from "vue";
import BreadCrumb from "../../components/BreadCrumb.vue";
import BomCanvas from "../home/<USER>/bomCanvas.vue";
import { useRoute, useRouter } from "vue-router";
import { Plus, Delete, Close, Check } from "@element-plus/icons-vue";
import { ElLoading,ElMessage,ElMessageBox, dayjs } from "element-plus";
import { useRouteParamsStore } from "../../stores/useRouteParamsStore.js";
import {
  api_post_spartPage,
  api_post_shopCarOrderSave,
  api_post_queryShopOrderInfo,
  api_post_show_data,
  api_dealer_account_list,
  api_dealer_company_list,
  formatPrice
} from "../../utils/api";
import router from "../../router/index.js";
import SortIcon from "../../components/sortIcon.vue";
const rpStore = useRouteParamsStore();
const route = useRoute();
const props = defineProps({
  params: {
    type: Object,
    default: null,
  },
});
const formInline = ref({
  id: "",
  salesperson:"",
  customer:"",
  collectAddress:"",
  billingAddress:"",
  createDate: new Date().toISOString().substring(0, 10),
  contactName:"",
  contactPhone:"",
  contactEmail:"",
  remarks: "",
});
const base_downFileByPath_url = inject("$base_downFileByPath_url");
const loading = ref(false);
const modaLoading = ref(false);
const tableData = ref([]);
const dealerAccountData = ref([]);
const dealerCompanytData = ref([]);
const userInfo = ref(
  JSON.parse(window.sessionStorage.getItem("userInfo")) || {}
);

const dialogSelectTable = ref([]);
const dialogFormVisible = ref(false);
const dialogSearch = ref({
  name: "",
});
const dialogAllTable = ref([]);
const isEdit = ref(true);
const currentSortKey = ref("");

/*  --BomCanvas组件下--*/
const bomCanvasRef = ref();
const paramsBomCanvas = ref({
  id:"",
  existsCart:"",
  existsFavorites:"",
  filePath: "",
  bomId:"",
  materielCode:"",
  materielNameEn:"",
  materielName:"",
  indexNo:0,
  quantity:0,
  amount:0,
  price:0
});
const dialogBomCanvasVisible = ref(false);
const handleBomCanvasModalClose = (done) => {
  dialogBomCanvasVisible.value=false;
};

const breadcrumbUrl = computed(() =>
    route.query.id ? `/createOrder?id=${route.query.id}` : '/createOrder'
);

const jumpDetail = (item) => {

  console.log(item);

  dialogBomCanvasVisible.value=true;
  paramsBomCanvas.value.filePath=item.filePath;
  paramsBomCanvas.value.bomId=item.bomId;
  paramsBomCanvas.value.materielCode=item.materielCode;
  paramsBomCanvas.value.materielNameEn=item.materielNameEn;
  paramsBomCanvas.value.materielName=item.materielName;
  paramsBomCanvas.value.quantity=item.quantity;
  paramsBomCanvas.value.price=item.unitPrice || item.price;
  paramsBomCanvas.value.indexNo=item.indexNo;
  paramsBomCanvas.value.existsFavorites=item.existsFavorites;
  paramsBomCanvas.value.existsCart=item.existsCart;
  paramsBomCanvas.value.id = item.bomItemsId;

  //---从订单列表  点击details查看订单时  item.type为空
  if (item.type === undefined || item.type === null || item.type === '') {
    paramsBomCanvas.value.existsCart="未知";
    paramsBomCanvas.value.existsFavorites="未知";
    return
  }

  //---从购物车和收藏夹过来  准备创建订单的时候
  if(item.type=="2"){
    //1购物车 2收藏夹
    paramsBomCanvas.value.existsFavorites="1";
    //注意：这里existsFavorites=1是购物车和收藏夹互相存在的意思
    paramsBomCanvas.value.existsCart=item.existsFavorites;
  }else{
    paramsBomCanvas.value.existsCart="1";
    //注意：这里existsFavorites=1是购物车和收藏夹互相存在的意思
    paramsBomCanvas.value.existsFavorites=item.existsFavorites;
  }


};
const currentOrderId = ref('')
/*  --BomCanvas组件上--*/
onMounted(() => {

  //  通过 父组件 或者 页面跳转
  currentOrderId.value = route.query.id|| props.params?.id ;
  tableData.value = rpStore.getParam("selectedList") || [];
  if("2" == userInfo.value.userType){
    if(!currentOrderId.value){
      //经销商账号 自动赋值
      api_post_show_data({...userInfo.value}).then((data) => {
        formInline.value.collectAddress = data.street;
        formInline.value.billingAddress = data.street;
        formInline.value.contactName = data.contactName;
        formInline.value.contactEmail = data.email;
        formInline.value.contactPhone = data.phone;
      });
    }
  }
  getDetail();
  if("1" == userInfo.value.userType){
    dealerAccountList();
    dealerCompanyList();
  }

});

const getDetail = () => {

  let loading;
  if(currentOrderId.value){
    //有id，修改不让编辑
    isEdit.value = false;
    loading = ElLoading.service({
      lock: true,
      text: "Loading...",
      background: "rgba(255, 255, 255, 0.7)",
    });
  }else {
    //新增可以编辑
    isEdit.value = true;
  }

  console.log(currentOrderId.value);
  currentOrderId.value &&
    api_post_queryShopOrderInfo({
      orderId: currentOrderId.value,
    }).then((res) => {
      console.log("----getDetail---------");
      console.log(res);
      if (res.code == 0) {
        formInline.value.id = res.data.id;
        formInline.value.createDate =  dayjs(res.data.createDate).format("YYYY-MM-DD");
        formInline.value.deliveryTime = dayjs(res.data.deliveryTime).format("YYYY-MM-DD");
        formInline.value.billingAddress = res.data.billingAddress;
        formInline.value.payment =  dayjs(res.data.payment).format("YYYY-MM-DD");

        formInline.value.collectAddress = res.data.collectAddress;
        formInline.value.contactPhone = res.data.contactPhone;
        formInline.value.contactName = res.data.contactName;
        formInline.value.contactEmail = res.data.contactEmail;

        formInline.value.remarks = res.data.remarks;
        tableData.value = res.data.productList.map((item) => ({
          ...item,
          cartList: item.salesOrderItemList.map((item) => ({
            ...item,
            bomItemsId: item.linkId,
            materielCode: item.materielCode,
          })),
        }));
      }

      loading.close();
    });
};

const dealerAccountList =() =>{
  api_dealer_account_list()
      .then((result) => {
        console.log("---Account---");
        console.log(result);
        console.log("---Account---");
        dealerAccountData.value = result;
      })
}
//dealerAccountChange
const dealerAccountChange = (selectedId) => {
  console.log('选中对象的Id:', selectedId);
  var account = dealerAccountData.value.filter((item)=>item.id == selectedId);
  console.log('完整选中对象:', account);
  formInline.value.customer = account[0].customerId;
  var company_dealer = dealerCompanytData.value.filter((item)=>item.id == account[0].customerId);

  formInline.value.collectAddress =  company_dealer[0].address;
  formInline.value.contactPhone = company_dealer[0].telephone;
  formInline.value.contactName = company_dealer[0].linkman;
  formInline.value.contactEmail = company_dealer[0].email;

}
//dealerCompanytData
const dealerCompanyList =() =>{
  api_dealer_company_list()
      .then((result) => {
        console.log("---Company---");
        console.log(result);
        console.log("---Company---");
        dealerCompanytData.value = result;
      })
}

const deleteTableItem = ({ commodityItem, item, index }) => {
  commodityItem.cartList.splice(index, 1);
  if (commodityItem.cartList.length <= 0) {
    tableData.value.splice(tableData.value.indexOf(commodityItem), 1);
  }
};

const getTableIndex = (itemIndex, index) => {
  return (
    Array(itemIndex)
      .fill(null)
      .reduce(
        (total, current, currentIndex) =>
          total + (tableData.value?.[currentIndex]?.cartList?.length || 0),
        0
      ) +
    index +
    1
  );
};

const onSort = ({ type, key }) => {
  currentSortKey.value = key;
  const sort = (sortArray) => {
    sortArray.sort((a, b) => {
      if (type === "asc") {
        return a[key].localeCompare(b[key]);
      } else {
        return b[key].localeCompare(a[key]);
      }
    });
  };
  sort(tableData.value);
  tableData.value.forEach((item) => {
    sort(item.cartList);
  });
};

const getImageUrl = (array, type) => {
  const data = array.reduce((total: string[], current: string) => {
    const urlArray = current.split("/").reverse();
    const isProduct = type !== "all" ? urlArray[0].includes("product") : true;
    if (
      urlArray.length > 0 &&
      isProduct &&
      (urlArray[0].includes(".jpg") || urlArray[0].includes(".png"))
    ) {
      total.push(base_downFileByPath_url + current);
    }
    return total;
  }, []);
  // console.log(data, "datadatadata");
  return data;
};

const downloadExcelTemplate =() =>{
  const a = document.createElement('a');
  var currentDomain = window.location.origin;
  // 拼接完整的链接
  var fullUrl = currentDomain + "/fileviewer/_Files/template/Order_Excel_Import.xlsx";
  a.href =  fullUrl;
  a.download = 'Order_Excel_Import.xlsx'; // 设置下载文件的文件名
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

const newInsertToTableData = (data) => {
  data.forEach((selectItem) => {
    const table = tableData.value;
    const current = table.find((item) => item.id === selectItem.id);
    if (!current) {
      table.push({
        ...selectItem,
        cartList: [
          ...selectItem.cartList.map((item) => ({
            ...item,
           /* bomItemsId: item.id,*/
          })),
        ],
      });
    } else {
      selectItem.cartList.forEach((el) => {
        if (!current.cartList.find((item) => item.materielCode === el.materielCode)) {
          current.cartList.push(
              { ...el,
                /*bomItemsId: el.id*/
              });
        }
      });
    }
  });

};

const onSuccess = (res) => {
  loading.value = false;
  debugger;
  if (res.code == 0) {
    //tableData.value = res.data;

    newInsertToTableData(res.data);
    ElMessage.success("Import Successfully");
  }else{
    ElMessageBox.alert(
        res.msg,
        "Tip:",
        {
          confirmButtonText: "OK",
          type: "warning",
        }
    );
  }
};

const beforeUpload = (file) => {
  // 在上传文件之前调用，显示loading
      loading.value = true;
  // 如果需要对file进行检查或者其他操作，可以在这里实现
  // 如果返回false，则停止上传
  return true;
};

/**  弹窗相关 */
const getDialogList = () => {
  modaLoading.value = true;
  api_post_spartPage({
    key: dialogSearch.value.name,
    pageSize: 100,
  })
    .then((res) => {
      console.log(res, "11111");
      dialogAllTable.value = res.data || [];
    })
    .finally(() => {
      modaLoading.value = false;
    });
  // dialogAllTable.value = [];
};

const handleModalClose = (done) => {
  dialogSelectTable.value = [];
  done && done();
  dialogAllTable.value = [];
  dialogSearch.value.name = "";
};

const openDialog = () => {
  dialogFormVisible.value = true;
  dialogSelectTable.value = [];
  getDialogList();
};
const searchTableData = (val) => {
  getDialogList();
};
const addMaterial = ({ commodityItem, item, index }) => {
  const selectTable = dialogSelectTable.value;
  let currentcommodityItem = selectTable.find(
    (item) => item.id === commodityItem.id
  );
  if (!currentcommodityItem) {
    selectTable.push({
      ...commodityItem,
      spartList: [item],
    });
  } else {
    const current = currentcommodityItem.spartList.find(
      (el) => el.id === item.id
    );
    if (current) {
      ElMessage.warning("The material has been selected");
    } else {
      currentcommodityItem.spartList.push(item);
    }
  }

  // if () {
  //   ElMessage.warning("The material has been selected");
  // } else {
  //   dialogSelectTable.value.push(row);
  // }
};

const deleteMaterial = ({ commodityItem, item, index }) => {
  commodityItem.spartList.splice(index, 1);
  if (commodityItem.spartList.length <= 0) {
    dialogSelectTable.value.splice(
      dialogSelectTable.value.indexOf(commodityItem),
      1
    );
  }
};

const confirmHandler = () => {
  dialogSelectTable.value.forEach((selectItem) => {
    const table = tableData.value;
    const current = table.find((item) => item.id === selectItem.id);
    if (!current) {
      table.push({
        ...selectItem,
        cartList: [
          ...selectItem.spartList.map((item) => ({
            ...item,
            bomItemsId: item.id,
          })),
        ],
      });
    } else {
      selectItem.spartList.forEach((el) => {
        if (!current.cartList.find((item) => item.id === el.id)) {
          current.cartList.push({ ...el, bomItemsId: el.id });
        }
      });
    }
  });
  dialogFormVisible.value = false;
};

const exitPage = () => {
  router.push("/orderList"); // 直接跳转回订单列表
};

const save = async () => {
  loading.value = true;

  if("1" == userInfo.value.userType){
    if(! formInline.value.salesperson || !formInline.value.customer){
      ElMessage.warning("Dealer Account data is abnormal!");
      loading.value = false;
      return ;
    }
  }

  if (tableData.value.length <= 0) {
    ElMessage.warning("Details cannot be empty!");
    loading.value = false;
    return ;
  }

  var orderInfor = {
    ...formInline.value,
    shopList: tableData.value.reduce((total, current) => {
      total.push(
        ...current.cartList.map((item) => ({
          id: formInline.value.id ? item.id : "",
          shopCartId: item.shopId,
          bomItemsId: item.bomItemsId,
          amount: item.amount,
          productType: item.productType,
          remarks: item.remarks,
          materielCode: item.materielCode,
        }))
      );
      return total;
    }, []),
  };

  // 找到重复的 materielCode 并拼接
  const materielCodeMap = {};
  orderInfor.shopList.forEach((item) => {
    if (materielCodeMap[item.materielCode]) {
      materielCodeMap[item.materielCode].push(item);
    } else {
      materielCodeMap[item.materielCode] = [item];
    }
  });

  // 提取重复的 materielCode 信息
  const duplicateMaterielCodes = Object.keys(materielCodeMap)
      .filter((key) => materielCodeMap[key].length > 1)
      .join(", ");


  api_post_shopCarOrderSave(orderInfor)
    .then((res) => {
      router.push("/orderList");
      if (res.code == 1) {
        //1失败
        ElMessage.warning(res.msg);
      } else {
        ElMessage.success("Successfully");
        // 如果有重复的 materielCode，弹窗提示
        if (duplicateMaterielCodes) {
          ElMessageBox.alert(
              `Duplicate part number have been automatically merged: ${duplicateMaterielCodes}`,
              "Tip:",
              {
                confirmButtonText: "OK",
                type: "warning",
              }
          );
        }

      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const multiply = (a, b) => {
  // 将数字转换为字符串
  const aStr = a.toString();
  const bStr = b.toString();

  // 计算小数位数
  const aDecimals = aStr.includes(".") ? aStr.split(".")[1].length : 0;
  const bDecimals = bStr.includes(".") ? bStr.split(".")[1].length : 0;

  // 总的小数位数
  const totalDecimals = aDecimals + bDecimals;

  // 将数字转换为整数
  const aInt = Number(aStr.replace(".", ""));
  const bInt = Number(bStr.replace(".", ""));

  // 进行整数相乘
  const product = aInt * bInt;

  // 调整小数位数
  const result = product / Math.pow(10, totalDecimals);

  // 保留两位小数，并转换为数字类型
  return Number(result.toFixed(2));
};
</script>

<style lang="less" scoped>
.head-all-item-view {
  display: flex;
}
.head-all-item {
  height: 39px;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border: 1px solid #e8e8e8;
  margin: 10px 0;

  // padding-left: 16px;
  &.small {
    height: 32px;
    font-size: 12px;
  }

  &-view {
    &.checkbox {
      width: 64px;
    }

    &.name {
      flex: 3;
      margin-right: 16px;
    }

    &.materierCode {
      flex: 2;
      margin-right: 16px;
    }

    &.quantity {
      flex: 3;
      margin-right: 16px;
    }

    &.amount {
      flex: 1;
      margin-right: 16px;
    }

    &.unitPrice {
      flex: 1;
      margin-right: 16px;
    }

    &.remarks {
      flex: 3;
      margin-right: 16px;
    }

    &.operate {
      margin-right: 0;
      width: 100px;
    }
  }
}

.shopping-cart-item-all {
  border-radius: 8px;
  margin-bottom: 16px;

  .item-all-name {
    align-items: center;
    background-color: #f3f6f8;
    border: 1px solid #f0f3f5;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    display: flex;
    flex-direction: row;
    height: 48px;
    overflow: hidden;
    padding-left: 16px;
    color: #11192d;
    font-size: 14px;
    font-weight: 500;

    &.small {
      height: 32px;
      font-size: 12px;
    }
  }

  .item-all-checkbox {
    margin-right: 16px;
  }

  .item-all-content {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    border-color: #f0f3f5;
    border-style: solid;
    border-width: 1px;
    position: relative;
    padding-left: 16px;
    word-break: break-all;

    .cart-all-item {
      align-items: flex-start;
      display: flex;
      position: relative;
      align-items: center;
      margin: 8px 0;

      .item-all-checkbox {
        width: 48px;
      }

      .cart-all-item-info {
        flex: 3;
        align-items: center;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        margin-right: 16px;
        padding-top: 2px;

        .cart-all-item-img {
          width: 46px;
          height: 46px;
          margin-right: 12px;
          background-color: rgba(0, 0, 0, 0.02);
          position: relative;
        }

        .cart-all-item-name {
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          color: #11192d;
          display: -webkit-box;
          font-size: 14px;
          font-weight: 500;
          line-height: 20px;
          margin-bottom: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .cart-all-item-view {
        margin-right: 16px;

        &.materierCode {
          flex: 2;
        }

        &.quantity {
          flex: 3;
        }

        &.amount {
          flex: 1;
        }

        &.unitPrice {
          flex: 1;
          color: #ff5000;
          font-size: 18px;
          font-weight: 600;
        }

        &.remarks {
          flex: 3;
        }

        &.operate {
          margin-right: 0;
          width: 100px;

          .operate-item {
            display: block;
          }
        }
      }
    }
  }
}
.wb-image {
  /deep/ .el-image-viewer__canvas {
    .el-image-viewer__img {
      background: #fff;
    }
  }
}

</style>
