<template>
  <!-- <BreadCrumb /> 下-->
  <BreadCrumb
      :list="[
      {
        url: '/notice',
        name: 'Notice',
      },
    ]"
  />
  <!-- <BreadCrumb /> 上-->
  <div style="padding: 0 16px">
    <div class="search-warp">
      <div class="search-view" style="margin-right: 26px">
        <el-button type="primary" @click="searchNoticeTable()"
          >Search</el-button
        >
        <el-input
          v-model="params.materielCode"
          placeholder="Part Number"
          class="search-input"
        >
        </el-input>
      </div>
<!--      <div class="search-view" style="margin-right: 26px">
        <el-date-picker
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            placeholder="Start date"
            style="width: 100%"
            v-model="params.startDate"
        ></el-date-picker>
      </div>
      <div class="search-view" style="margin-right: 26px">
        <el-date-picker
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            placeholder="End date"
            style="width: 100%"
            v-model="params.endDate"
        ></el-date-picker>
      </div>-->
      <div class="search-view">
        <el-radio-group  v-model="params.readFlag" style="border:1px solid rgb(192,196,204);padding: 0 26px">
          <el-radio label="">All</el-radio>
          <el-radio label="0">Unread</el-radio>
          <el-radio label="1">read</el-radio>
        </el-radio-group>
      </div>
    </div>
    <div class="home-warp" style="height: calc(100vh - 130px)">
      <div class="home-table-view">
        <div class="home-table-control">
          <el-select
            placeholder="Equipment/Engine"
            v-model="brandId"
            style="margin-right: 16px"
            clearable
            filterable
            @change="selectChange('brand',$event)"
          >
            <el-option
              v-for="item in getSelectList('brand')"
              :key="item.id"
              :label="item.fileName"
              :value="item.id"
            />
          </el-select>
          <el-select
            placeholder="Equipment Type"
            v-model="eqId"
            style="margin-right: 16px"
            clearable
            filterable
            @change="selectChange('eq',$event)"
          >
            <el-option
              v-for="item in getSelectList('eq', brandId)"
              :key="item.id"
              :label="item.fileName"
              :value="item.id"
            />
          </el-select>
          <el-select
            style="margin-right: 16px"
            placeholder="Equipment Model"
            v-model="modelId"
            @change="selectChange('model',$event)"
            clearable
            filterable
          >
            <el-option
              v-for="item in getSelectList('model', eqId)"
              :key="item.id"
              :label="item.fileName"
              :value="item.id"
            />
          </el-select>
          <el-select
            placeholder="Service Code"
            v-model="versionId"
            @change="selectChange('version',$event)"
            clearable
            filterable
          >
            <el-option
              v-for="item in getSelectList('version', modelId)"
              :key="item.id"
              :label="item.fileName"
              :value="item.id"
            />
          </el-select>
        </div>
        <el-scrollbar style="height: calc(100% - 48px)">
          <div class="home-table-scroll">
            <el-table
                class="home-table"
                :data="tableData"
                style="width: 100%"
                default-expand-all
                :header-row-style="{
                    height: '32px',
                  }"
                :header-cell-style="{
                    background: '#f5f5f5',
                    fontWeight: 'normal',
                    color: '#213547',
                    fontSize: '12px',
                  }"
            >
              <el-table-column label="Creation Time" prop="createDate" width="150px" />
              <el-table-column label="Status" prop="status" width="150px">
                <template #default="props"
                >
                  <div    :style="{
                            color: props.row.readFlag == '1' ? 'rgb(64,158,255)' : '#ff5000',
                            fontSize: '13px',
                            fontWeight: '600'
                          }">
                  {{ props.row.readFlag  == '1'?"Read":"Unread" }}
                  </div>

                </template>
              </el-table-column>
              <el-table-column label="PDF" show-overflow-tooltip>
                <template #default="props">

                  <el-button
                      @click="setAsRead(props.row)"
                      link
                      type="primary"
                      size="small"
                      style="font-size: 14px; font-weight: 600;text-decoration: underline;"
                  >
                    <img
                        style="width: 20px"
                        src="../../assets/images/main/pdf.svg"
                    />

                  </el-button>
                 <a  @click="setAsRead(props.row)" style="text-decoration: underline">{{ props.row.files?.split('/').pop()}}</a>
                </template>
              </el-table-column>
              <el-table-column label="Description" prop="description" show-overflow-tooltip />

              <el-table-column label="Equipment Model" prop="model" />
              <!-- <el-table-column
                  label="Specification"
                  show-overflow-tooltip
                  prop="materielSpecification"
              />-->
              <el-table-column label="Service Code" prop="bom" />
              <el-table-column label="Part Number" prop="materielCode" />
              <el-table-column label="Serial Number Range" prop="serialNumberRange" />
<!--              <el-table-column label="Old Part Number" prop="materielCodeOld" />-->
            </el-table>
            <div style="display: flex; justify-content: flex-end; margin-top: 8px">
              <el-pagination
                  layout="prev, pager, next"
                  :total="params.total"
                  :page-size="params.pageSize"
                  @current-change="currentPageChange($event)"
                  :current-page="params.pageNo"
              />
            </div>
          </div>
        </el-scrollbar>
      </div>

    </div>
  </div>
</template>
<script setup lang="ts">
import {onMounted, ref, nextTick, inject} from "vue";
import BreadCrumb from "../../components/BreadCrumb.vue";

import { ElLoading,ElMessage,ElMessageBox, dayjs } from "element-plus";
import {
  api_get_menu,
  api_post_pmcNotice_myNotice,
  api_post_pmcNotice_readNotice,
  api_post_pmcNotice_noticeNumber,
} from "../../utils/api";


// 注入全局变量
const notice_noRead = inject('notice_noRead');

const params = ref({
  id:"",
  materielCode: "",
  readFlag:"",
  brand:"",
  productType:"",
  model:"",
  bom:"",
  startDate:null,
  endDate:null,
  total:10,
  pageNo: 1,
  pageSize: 10,
});


const isEq = ref(true);
const tableData = ref([]);

/*产品model bom 选择下拉框----下---- */
/*产品model bom 选择下拉框----下---- */
const brandId = ref("");
const eqId = ref("");
const modelId = ref("");
const versionId = ref("");
const menu = ref([]);

const findNodeById = (tree, id) =>  {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].id === id) {
      return tree[i];
    }
    if (tree[i].children) {
      const found = findNodeById(tree[i].children, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
}

const getSelectList = (type, parentid) => {
  if (type === "brand") {
    return menu.value;
  }

  if (type === "eq") {
    const brandList =menu.value;
    return brandList.reduce((total, current) => {
      if (parentid) {
        if (current.id === parentid) {
          return [...total, ...(current.children || [])];
        }
      } else {
        return [...total, ...(current.children || [])];
      }
      return total;
    }, []);
  }

  if (type === "model") {
    const eqList = getSelectList("eq", brandId.value);
    return eqList.reduce((total, current) => {
      if (parentid) {
        if (current.id === parentid) {
          return [...total, ...(current.children || [])];
        }
      } else {
        return [...total, ...(current.children || [])];
      }
      return total;
    }, []);
  }

  if (type === "version") {
    const modelList = getSelectList("model", eqId.value);
    console.log(modelList,"modelListmodelListmodelList")
    return modelList.reduce((total, current) => {
      if (parentid) {
        if (current.id === parentid) {
          return [...total, ...(current.children || [])];
        }
      } else {
        return [...total, ...(current.children || [])];
      }
      return total;
    }, []);
  }
};

const selectChange = (type,selectedValue) => {
  switch (type) {
    case "brand":
      eqId.value = "";
      modelId.value = "";
      versionId.value = "";

      if(selectedValue){
        params.value.brand= findNodeById(menu.value,selectedValue).fileName;
      }else{
        params.value.brand="";
      }
      params.value.productType="";
      params.value.model="";
      params.value.bom="";

      break;
    case "eq":
      modelId.value = "";
      versionId.value = "";

      if(selectedValue){
        params.value.productType= findNodeById(menu.value,selectedValue).fileName;
      }else{
        params.value.productType="";
      }
      params.value.model="";
      params.value.bom="";
      break;
    case "model":
      versionId.value = "";

      if(selectedValue){
        params.value.model= findNodeById(menu.value,selectedValue).fileName;
      }else{
        params.value.model="";
      }
      params.value.bom="";
      break;
    case "version":
      if(selectedValue){
        params.value.bom= findNodeById(menu.value,selectedValue).fileName;
      }else{
        params.value.bom="";
      }

      break;
  }

  params.value.id =
      versionId.value || modelId.value || eqId.value || brandId.value;

  //复用前面不改，在这里进行其它对象的赋值

};
/*产品model bom 选择下拉框----上---- */
/*产品model bom 选择下拉框----上---- */

const searchNoticeTable = () => {

  let loading;
  loading = ElLoading.service({
    lock: true,
    text: "Loading...",
    background: "rgba(255, 255, 255, 0.7)",
  });

  api_post_pmcNotice_myNotice(params.value)
      .then((result) => {
        tableData.value = result.data;
        params.value.pageNo = result.pageNo;
        params.value.total = result.total;
        console.log(result.data);
      })
      .finally(() => {
        loading.close();
      });

      /*查询通知数量*/
      api_post_pmcNotice_noticeNumber().then((res)=>{
        if(res){
          notice_noRead.value = res.length;
        }
      });

};

const currentPageChange = (currentPage) => {

  params.value.pageNo = currentPage;
  searchNoticeTable();

};


onMounted(() => {

  api_get_menu().then((res) => {
    menu.value = res;
  });

  searchNoticeTable();

});

const setAsRead = (row) => {

  /*  if(row.readFlag =="1"){
      ElMessage.success("The current status is Read!");
      return;
    }*/
  var filePath = row.files;
  if (filePath) {
    var currentDomain = window.location.origin;
    // 拼接完整的链接
    var fullUrl = currentDomain + "/fileviewer/" + filePath;

    // 在新标签页中跳转到拼接后的链接
    window.open(fullUrl, '_blank');
  }

  api_post_pmcNotice_readNotice(
      {
        recordId: row.recordId,
      }
  )
      .then((res) => {
        if (res) {
        }


      })
      .finally(() => {
        searchNoticeTable();
      });
};



</script>
<style lang="less" scoped>
.search-warp {
  display: flex;
  align-items: center;
  margin: 16px 0;
  .search-view {
    display: flex;
    align-items: center;
    flex: 1;
    .search-input {
      flex: 1;
      margin-left: 8px;
    }
  }
}
.home-warp {
  display: flex;
  .home-table-view {
    flex: 1;
    border: 1px solid #ccc;
    .home-table-control {
      display: flex;
      padding: 8px 6px;
      ::v-deep .el-select__placeholder {
        color: #f89898; /* 修改 placeholder 颜色 */
      }
    }
    .home-table-scroll {
      padding-bottom: 0;
      padding: 0px 6px;
    }
  }
  .home-recommended-view {
    margin-left: 16px;
    width: 200px;
    border: 1px solid #ccc;
    padding-top: 32px;
    position: relative;
    .recommended-title {
      text-align: center;
      background-color: #eee;
      height: 32px;
      line-height: 32px;
      font-weight: 600;
      position: absolute;
      left: 0;
      right: 0;
      width: 100%;
      top: 0;
    }
    .recommended-warp {
      padding-top: 16px;
      font-size: 12px;
      .recommended-item {
        color: rgb(121.3, 187.1, 255);
        padding-top: 6px;
        margin: 0 16px;
        text-align: center;
        border: 1px solid #eee;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
        margin-bottom: 12px;
        &-bottom {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          background: rgba(0, 0, 0, 0.07);
          padding: 5px 6px;
          font-weight: 600;
          color: #000;
          font-size: 14px;
        }
      }
    }
  }
}


.home-table {
  /deep/.el-table__expand-column {
    > div {
      display: none;
    }
  }
  /deep/.el-table__header {
    background: #f5f5f5;
  }
  /deep/ .el-table__expanded-cell {
    padding: 0;
  }
  /deep/ th {
    padding: 0;
  }
}
.scrollbar-flex-content {
  display: flex;
}
.eq-image-warp {
  display: flex;
  flex-direction: column;
  margin-right: 16px;
  border: 2px solid #ececec;
  text-align: center;
  .eq-image-name {
    background-color: #ececec;
    padding: 4px 0 24px 12px;
    width: 100%;
    max-height: 50px;
    background-color: #ececec;
    height: 42px;
    padding: 8px;
    text-align: left;
  }
  .eq-image-item {
    padding: 6px;
    height: 100%;
  }
}
</style>
