import axios from "axios";
import { ElMessage } from "element-plus";
import { $base_host } from "../main.js";
import router from "../router";
//const baseURL = 'http://localhost:3004/sparts-wb'
const baseURL = $base_host;

const request = axios.create({
  baseURL,
  timeout: 30000,
});

request.interceptors.request.use(
  (config) => {
    config.headers = {
      ...config.headers,
      Accept: "*/*",
      /*Connection: 'keep-alive',*/
    };

    // 如果需要设置 cookie，可以在这里设置
    // const cookie = document.cookie;
    // if (cookie) {
    //   config.headers['Cookie'] = cookie;
    // }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

let isLoginErrorCount = 0;
let currentMillisecond = 0;
request.interceptors.response.use(
  (response) => {
    console.log("status", response.status);
    if (response.config.responseType === 'blob') {
      // 直接返回 Blob，不做进一步处理
      return response.data;
    }
    switch (response.status) {
      case 200:
        // 成功状态码
        if (response?.data?.includes?.("未登录或登录超时。请重新登录，谢谢")) {
            window.sessionStorage.removeItem("userInfo");
   /*       const now = Date.now();
          // 超过时间间隔则重置计数器
          if (now - currentMillisecond > 3000) {
            isLoginErrorCount = 0;
            currentMillisecond = now;
          }
          // 3秒内仅展示第一个错误提示
          if (isLoginErrorCount < 1) {
            ElMessage.error("failed! please log in again");
            isLoginErrorCount++;
          }

          setTimeout(() => {
            // 使用全新路径强制刷新页面
            const redirectUrl = window.location.origin + '/login'
            window.location.assign(redirectUrl)
          }, 1500)*/
          const redirectUrl = window.location.origin + '/login'
          window.location.assign(redirectUrl)
          return new Error(`User login failed, please log in again!`);
        }
        return response.data;
      case 500:
        ElMessage.error("Request failed!");
      case 500:
        ElMessage.error("Request failed!");
        break;
      default:
        console.error(`Unexpected status code: ${response.status}`);
    }
    // return Promise.reject(
    //   new Error(`Unexpected status code: ${response.status}`)
    // );
  },
  (error) => {
    console.log("status-err", error);
    switch (error.response.status) {
      case 500:
        ElMessage.error("Request failed!");
        break;
      default:
        console.error(`Unexpected status code: ${error.response.status}`);
    }
    return Promise.reject(
      new Error(`Unexpected status code: ${error.response.status}`)
    );
  }
);

export const get = async (
  url,
  params,
  headers = {
    "Content-Type": "application/x-www-form-urlencoded",
  },
  config = {}
) => {
  try {
    const response = await request.get(url, { params, headers, ...config });
    return response;
  } catch (error) {
    console.error("GET request error:", error);
    throw error;
  }
};

export const post = async (
  url,
  data,
  headers = {
    "Content-Type": "application/x-www-form-urlencoded",
  },
  isparams = true
) => {
  try {
    const formattedData = isparams
      ? new URLSearchParams(data).toString()
      : data;
    const response = await request.post(url, formattedData, { headers });
    return response;
  } catch (error) {
    console.error("POST request error:", error);
    throw error;
  }
};

// postFormData 方法
export const postFormData = async (url, data) => {
  try {
    const response = await request.post(url, data, {
      headers: {
        "Content-Type": undefined, // 不设置 Content-Type，让浏览器自动处理
      },
    });
    return response;
  } catch (error) {
    console.error("POST form data request error:", error);
    throw error;
  }
};

// Example usage:
// get('/some-endpoint', { param1: 'value1' });
// post('/some-endpoint', { key1: 'value1' });
