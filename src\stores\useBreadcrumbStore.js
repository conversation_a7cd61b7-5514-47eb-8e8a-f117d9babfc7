// store/useBreadcrumbStore.js
import { defineStore } from 'pinia'

export const useBreadcrumbStore = defineStore('breadcrumb', {
  state: () => ({
    breadcrumbs: [], // 面包屑路径数组
    products: [],
  }),
  actions: {
    setBreadcrumbs(breadcrumbs) {
      this.breadcrumbs = breadcrumbs
    },
    setProducts(products) {
      this.products = products
    },
    addBreadcrumb(breadcrumb) {
      const exists = this.breadcrumbs.some(
        (crumb) => crumb.id === breadcrumb.id,
      )
      if (exists) {
        return // 如果已经存在，直接返回
      }
      this.breadcrumbs.push(breadcrumb)
    },
    clearBreadcrumbs() {
      this.breadcrumbs = []
    },
  },
})
