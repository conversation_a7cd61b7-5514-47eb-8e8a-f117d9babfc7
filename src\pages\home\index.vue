<template>
  <!-- <BreadCrumb /> -->
  <div style="padding: 0 16px">
    <div class="search-warp">
      <div class="search-view" style="margin-right: 26px">
        <el-button type="primary" @click="searchEqTable('eq')"
          >Search The Equipment</el-button
        >
        <el-input
          v-model="params.eqSearchKey"
          placeholder="Eequipment Model,Description,Model_Number,Service_Code,Serial_Number"
          class="search-input"
        >
        </el-input>
      </div>
      <div class="search-view">
        <el-button type="primary" @click="searchEqTable('part')"
          >Search The Part</el-button
        >
        <el-input
          v-model="params.partSearchKey"
          placeholder="Part_Number,Part_Name,Description"
          class="search-input"
        >
        </el-input>
      </div>
    </div>
    <div class="home-warp" style="height: calc(100vh - 130px)">
      <div class="home-table-view">
        <div class="home-table-control">
          <el-select
            placeholder="Please Select"
            v-model="brandId"
            style="margin-right: 16px"
            clearable
            filterable
            @change="selectChange('brand')"
          >
            <el-option
              v-for="item in getSelectList('brand')"
              :key="item.id"
              :label="item.fileName"
              :value="item.id"
            />
          </el-select>
          <el-select
            placeholder="Equipment Type"
            v-model="eqId"
            style="margin-right: 16px"
            clearable
            filterable
            @change="selectChange('eq')"
          >
            <el-option
              v-for="item in getSelectList('eq', brandId)"
              :key="item.id"
              :label="item.fileName"
              :value="item.id"
              clearable
            />
          </el-select>
          <el-select
            style="margin-right: 16px"
            placeholder="Equipment Model"
            v-model="modelId"
            @change="selectChange('model')"
            clearable
            filterable
          >
            <el-option
              v-for="item in getSelectList('model', eqId)"
              :key="item.id"
              :label="item.fileName"
              :value="item.id"
            />
          </el-select>
          <el-select
            placeholder="Service Code"
            v-model="versionId"
            @change="selectChange('version')"
            clearable
            filterable
          >
            <el-option
              v-for="item in getSelectList('version', modelId)"
              :key="item.id"
              :label="item.fileName"
              :value="item.id"
            />
          </el-select>
        </div>
        <el-scrollbar style="height: calc(100% - 48px)">
          <div class="home-table-scroll">
              <!--设备搜索组件-->
            <HomeEquipmentTable :params="params" ref="eqTableRef" v-if="isEq" />
              <!--配件搜索组件-->
            <HomePartTable :params="params" ref="partTableRef" v-if="!isEq" />
          </div>
        </el-scrollbar>
      </div>

      <div class="home-recommended-view">
        <div class="recommended-title">Recommended Parts</div>
        <el-scrollbar max-height="100%">
          <div class="recommended-warp">
            <div class="recommended-item" v-for="item in recommendList">
              <div>{{ item.materielCode }}</div>
              <div >{{ item.materielNameEn }}</div>
              <div style="margin-bottom: 4px">
                <el-button  v-if="item.bomId && item.filePath && item.indexNo"
                            @click="jumpDetail(item)"
                            link
                            type="primary"
                            size="small"
                            style="font-size: 12px; font-weight: 600;text-decoration: underline;"
                >

                     <span style="color: #f56c6c">Expl.View</span>
                </el-button>
              </div>
              <div class="recommended-item-bottom">

                <!-- 动态显示币种符号 -->
                {{ userInfo.currency === '1' ? '¥' : userInfo.currency === '2' ? '$' : userInfo.currency === '3' ? '€' : '' }}
                {{ formatPrice(item.price) }}
               <a @click="addCart(item)">
                   <img v-if="item.existsCart == '1'"  style="vertical-align: bottom; position: relative; top: -3px; margin-left: 2px;"
                        src="../../assets/images/main/purCarRed.svg"
                        alt="+"
                        height="15px"
                   />
                   <img v-else                          style="vertical-align: bottom; position: relative; top: -3px; margin-left: 2px;"
                        src="../../assets/images/main/purCarRedEmpty.svg"
                        alt="+"
                        height="15px"
                   />
               </a>
                <el-button v-if="item.existsCart == '1'"
                  :loading="item.buyLoad"
                  @click="addCart(item)"
                  type="danger"
                  size="small"
                  style="margin-left: 6px"
                  >
                  Remove
                  </el-button
                >
                <el-button v-else
                    :loading="item.buyLoad"
                    @click="addCart(item)"
                    type="danger"
                    size="small"
                    style="margin-left: 6px"
                >
                  Add To Cart
                </el-button
                >

              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>

  <!-- Diagram弹窗--BomCanvas   -->
  <el-dialog
      destroy-on-close
      v-model="dialogBomCanvasVisible"
      title="BOM Diagram"
      width="70%"
      top="1vh"
      :show-close="true"
      :before-close="handleBomCanvasModalClose"
  >
    <BomCanvas :params="paramsBomCanvas" ref="bomCanvasRef" />

  </el-dialog>
</template>
<script setup lang="ts">
import { onMounted, ref, nextTick } from "vue";
import BreadCrumb from "../../components/BreadCrumb.vue";
import BomCanvas from "../home/<USER>/bomCanvas.vue";
import HomeEquipmentTable from "./components/homeEquipmentTable.vue";
import HomePartTable from "./components/homePartTable.vue";
import { ElMessage } from "element-plus";
import {
  api_get_menu,
  api_get_bom_items_recommend,
  api_post_addToCart,
  formatPrice
} from "../../utils/api";

const eqTableRef = ref();
const partTableRef = ref();
const isEq = ref(true);
//默认选中Equipment 9033c9a9872d4106bf2f49f511210878
const brandId = ref("9033c9a9872d4106bf2f49f511210878");
const eqId = ref("");
const modelId = ref("");
const versionId = ref("");

const params = ref({
  eqSearchKey: "",
  id: brandId.value,
  partSearchKey: "",
});

const recommendList = ref([]);

const userInfo = ref(
    JSON.parse(window.sessionStorage.getItem("userInfo")) || {}
);


const searchEqTable = (type) => {
  //console.log("userInfo",userInfo.value);
  isEq.value = type === "eq";
  nextTick(() => {
    if (type == "eq") {
      eqTableRef.value.getTableList();
    } else {
      partTableRef.value.getTableList();
    }
  });
};

/*  --BomCanvas组件下--*/
const bomCanvasRef = ref();
const paramsBomCanvas = ref({
  id:"",
  existsCart:"",
  existsFavorites:"",
  filePath: "",
  bomId:"",
  materielCode:"",
  materielNameEn:"",
  materielName:"",
  indexNo:0,
  quantity:0,
  amount:0,
  price:0
});
const dialogBomCanvasVisible = ref(false);
const handleBomCanvasModalClose = (done) => {
  dialogBomCanvasVisible.value=false;
};
const jumpDetail = (item) => {

  console.log(item);

  dialogBomCanvasVisible.value=true;
  paramsBomCanvas.value.filePath=item.filePath;
  paramsBomCanvas.value.bomId=item.bomId;
  paramsBomCanvas.value.materielCode=item.materielCode;
  paramsBomCanvas.value.materielNameEn=item.materielNameEn;
  paramsBomCanvas.value.materielName=item.materielName;
  paramsBomCanvas.value.quantity=item.quantity;
  paramsBomCanvas.value.price=item.price;
  paramsBomCanvas.value.indexNo=item.indexNo;
  paramsBomCanvas.value.id = item.bomItemsId;
  //购物车 收藏夹
  paramsBomCanvas.value.existsCart=item.existsCart;
  paramsBomCanvas.value.existsFavorites=item.existsFavorites;


};
/*  --BomCanvas组件上--*/

const menu = ref([]);
onMounted(() => {
  api_get_menu().then((res) => {
    menu.value = res;
  });
  getRecommend();
});

const getRecommend = () => {
  api_get_bom_items_recommend().then((res) => {
    recommendList.value = res;
    console.log(res);
  });
};

const getSelectList = (type, parentid) => {
  if (type === "brand") {
    return menu.value;
  }
  if (type === "eq") {
    const brandList =menu.value;
    return brandList.reduce((total, current) => {
      if (parentid) {
        if (current.id === parentid) {
          return [...total, ...(current.children || [])];
        }
      } else {
        return [...total, ...(current.children || [])];
      }
      return total;
    }, []);
  }

  if (type === "model") {
    const eqList = getSelectList("eq", brandId.value);
    return eqList.reduce((total, current) => {
      if (parentid) {
        if (current.id === parentid) {
          return [...total, ...(current.children || [])];
        }
      } else {
        return [...total, ...(current.children || [])];
      }
      return total;
    }, []);
  }

  if (type === "version") {
    const modelList = getSelectList("model", eqId.value);
    console.log(modelList,"modelListmodelListmodelList")
    return modelList.reduce((total, current) => {
      if (parentid) {
        if (current.id === parentid) {
          return [...total, ...(current.children || [])];
        }
      } else {
        return [...total, ...(current.children || [])];
      }
      return total;
    }, []);
  }
};

const selectChange = (type) => {
  switch (type) {
    case "brand":
      eqId.value = "";
      modelId.value = "";
      versionId.value = "";
      break;
    case "eq":
      modelId.value = "";
      versionId.value = "";
      break;
    case "model":
      versionId.value = "";
      break;
    case "version":
      break;
  }
  params.value.id =
    versionId.value || modelId.value || eqId.value || brandId.value;
  searchEqTable(isEq.value ? "eq" : "part");
};

const addCart = (row) => {
  row.buyLoad = true;
  api_post_addToCart([
    {
      amount: 1,
      bomItemsId: row.bomItemsId,
      productType: "part",
    },
  ])
    .then((res) => {

      if ("0"==res.code) {
        row.existsCart="1";
        // row.existsFavorites="1";
        ElMessage.success({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else if(("1"==res.code) ){
        row.existsCart="0";
        // row.existsFavorites="0";
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else{
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }


    })
    .finally(() => {
      row.buyLoad = false;
    });
};
</script>
<style lang="less" scoped>
.search-warp {
  display: flex;
  align-items: center;
  margin: 16px 0;
  .search-view {
    display: flex;
    align-items: center;
    flex: 1;
    .search-input {
      flex: 1;
      margin-left: 8px;
    }
  }
}
.home-warp {
  display: flex;
  .home-table-view {
    flex: 1;
    border: 1px solid #ccc;
    .home-table-control {
      display: flex;
      padding: 8px 6px;
      ::v-deep .el-select__placeholder {
        color: #f89898; /* 修改 placeholder 颜色 */
      }
    }
    .home-table-scroll {
      padding-bottom: 0;
      padding: 0px 6px;
    }
  }
  .home-recommended-view {
    margin-left: 16px;
    width: 220px;
    border: 1px solid #ccc;
    padding-top: 32px;
    position: relative;
    .recommended-title {
      text-align: center;
      background-color: #eee;
      height: 32px;
      line-height: 32px;
      font-weight: 600;
      position: absolute;
      left: 0;
      right: 0;
      width: 100%;
      top: 0;
    }
    .recommended-warp {
      padding-top: 16px;
      font-size: 12px;
      .recommended-item {
        color: rgb(121.3, 187.1, 255);
        padding-top: 6px;
        margin: 0 16px;
        text-align: center;
        border: 1px solid #eee;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
        margin-bottom: 12px;
        &-bottom {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          background: rgba(0, 0, 0, 0.07);
          padding: 5px 6px;
          font-weight: 600;
          color: #000;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
