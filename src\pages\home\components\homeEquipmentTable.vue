<template>
  <el-table
    class="home-table"
    :data="tableData"
    style="width: 100%"
    default-expand-all
    :header-row-style="{
      height: '32px',
    }"
    :header-cell-style="{
      background: '#f5f5f5',
      fontWeight: 'normal',
      color: '#213547',
      fontSize: '12px',
      fontWeight: 'bold',
    }"
  >
    <el-table-column type="expand" :width="1">
      <template #default="props">
        <div style="padding: 6px 0">
          <el-scrollbar
            v-if="props?.row?.bomsFileList"
            always
            height="150px"
            :wrap-style="{
              'overflow-y': 'hidden',
            }"
          >
            <div class="scrollbar-flex-content">

              <div class="more-button-wrapper" v-if="props.row?.engineFileList?.length > 0">
                <div style="text-decoration: underline"
                    class="engine-link"
                    @click="toggleEngineImages(props.row)"
                >
                  Engine
                  <el-icon class="arrow-icon" :class="{ 'is-active': props.row.showEngineImages }">
                    <DArrowRight />
                  </el-icon>
                </div>
              </div>

              <div
                  class="eq-image-warp engine-image-warp"
                  v-for="item in (props.row.engineFileList||[])"
                  v-show="props.row.showEngineImages"
              >
                <div class="eq-image-name">{{ getImageName(item) }}</div>
                <div style="height: 100px; width: 180px">
                  <el-image
                      @click="jumpDetail(item, 'pdf')"
                      class="eq-image-item"
                      :src="'/fileviewer' + item"
                  >
                  </el-image>
                </div>
              </div>

              <div class="eq-image-warp" v-for="item in props.row.bomsFileList">
                <div class="eq-image-name">{{ getImageName(item) }}</div>
                <div style="height: 100px; width: 180px">
                  <el-image
                    @click="jumpDetail(item, 'pdf')"
                    class="eq-image-item"
                    :src="'/fileviewer' + item"
                  >
                  </el-image>
                </div>
              </div>

            </div>
          </el-scrollbar>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="Equipment" width="115px">
      <template #default="props">
        <el-image
          style="width: 100px"
          :src="getImageUrl(props.row.productFileList || [])[0] || ''"
          preview-teleported
          :preview-src-list="
            getImageUrl(props.row.productFileList || [], 'all')
          "
        />
      </template>
    </el-table-column>
    <el-table-column label="Model" prop="model" width="115px" header-align="center" />
    <el-table-column label="" prop="operate" width="115px" header-align="center">
      <template #default="props">
        <el-button
          @click="jumpDetail(props.row.filePath)"
          link
          type="primary"
          size="small"
          style="font-size: 14px; font-weight: 600;text-decoration: underline;"
        >
          Details
        </el-button>

        <el-button
          link
          type="primary"
          size="small"
          :loading="props.row.favoritLoad"
          @click.prevent="addFavorit(props.row, 'product')"
        >
          <img v-if="props.row.existsFavorites == '1'"
                 src="../../../assets/images/main/favorites.svg"
                alt="+"
                height="15px"
          />
          <img v-else
               src="../../../assets/images/main/favoritesEmpty.svg"
               alt="+"
               height="15px"
          />
        </el-button>

<!--        <el-button
          link
          type="primary"
          size="small"
          @click.prevent="addCart(props.row, 'product')"
          :loading="props.row.buyLoad"
        >

         <img v-if="props.row.existsCart == '1'"
               src="../../../assets/images/main/purCar.svg"
            alt="+"
            height="15px"
          />
          <img v-else
               src="../../../assets/images/main/purCarEmpty.svg"
               alt="+"
               height="15px"
          />
        </el-button>-->

      </template>
    </el-table-column>
    <el-table-column label="Quantity" prop="amount" width="180px"  header-align="center">
        <template #default="props">
            <el-input-number
                      v-model="props.row.amount"
                      :min="1"
                         @change="amountChange(props.row)"
                    />
        </template>
    </el-table-column>
    <el-table-column label="Price" prop="price" width="260px" header-align="center" >
      <template #default="props">

        <div style="display:flex;">
              <!-- 动态显示币种符号 -->
              <span STYLE="color: #ff5000; font-size: 16px; font-weight: 600;">
                    {{ userInfo.currency === '1' ? '¥' : userInfo.currency === '2' ? '$' : userInfo.currency === '3' ? '€' : '' }}
                {{ formatPrice(props.row.totalPrice) }}
                &nbsp;&nbsp;
              </span>

              <a @click="addCart(props.row, 'product')"
                 style="vertical-align: bottom; position: relative; top: 3px; margin-left: 2px;">
                  <img v-if="props.row.existsCart == '1'"
                       src="../../../assets/images/main/purCarRed.svg"
                       alt="+"
                       height="15px"
                  />
                  <img v-else
                       src="../../../assets/images/main/purCarRedEmpty.svg"
                       alt="+"
                       height="15px"
                  />
                &nbsp;
              </a>

              <el-button
                    type="danger"
                    size="small"
                    @click.prevent="addCart(props.row, 'product')"
                    :loading="props.row.buyLoad"
                >
                    <span style=""
                          v-if="props.row.existsCart == '1'">
                      Remove
                    </span>
                    <span style=""
                          v-else>
                      Add To Cart
                    </span>
              </el-button>

        </div>
      </template>
    </el-table-column>

    <el-table-column label="Description" prop="materielSpecification" header-align="center"  align="center"  min-width="200px" />

<!--    <el-table-column
      label="Specification"
      show-overflow-tooltip
      prop="materielSpecification"
    />-->
<!--    <el-table-column label="Model Number" prop="materielCode" />-->
    <el-table-column label="Service Code" prop="serviceCode" align="center" >
      <template #default="props">
        {{ props.row.serviceCode }}
        <div   v-if="userInfo.userType == 1"
               style="color:rgb(65,155,255);font-size:15px;text-decoration: underline">
                <span  v-if="userInfo.userType == 1 && props.row.warehouseAmount"
                       style="color:#f56c6c;text-decoration: underline">
                       stock:&nbsp;{{ formatStock(props.row.warehouseAmount) }}&nbsp;
                      <span v-if="props.row.shelfCode">(&nbsp;{{ props.row.shelfCode }}&nbsp;)</span>
                </span>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <div style="display: flex; justify-content: flex-end; margin-top: 8px">
    <el-pagination
      layout="prev, pager, next"
      :total="page.total"
      :page-size="page.size"
      @current-change="currentChange"
      :current-page="page.current"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, inject, defineProps, defineExpose } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useBreadcrumbStore } from "../../../stores/useBreadcrumbStore.js";
import { ElLoading, ElMessage } from "element-plus";
import { Plus, DArrowRight ,ArrowDown} from '@element-plus/icons-vue'

import {
  api_post_spartPage,
  api_post_productPage,
  api_post_addToFavorites,
  api_post_addToCart,
  formatPrice
} from "../../../utils/api";
const breadcrumbStore = useBreadcrumbStore();
const router = useRouter();
const props = defineProps({
  params: {
    type: Object,
    default: null,
  },
});
const base_downFileByPath_url = inject("$base_downFileByPath_url");
const tableData = ref([]);

const page = ref({
  current: 1,
  total: 5,
  size: 3,
});

const userInfo = ref(
    JSON.parse(window.sessionStorage.getItem("userInfo")) || {}
);

const getTableList = (type?: string) => {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(255, 255, 255, 0.7)",
  });
  api_post_productPage({
    parentId: props.params.id || "",
    key: props.params.eqSearchKey || "",
    pageNo: page.value.current,
    pageSize: page.value.size,
  })
    .then((res) => {
      tableData.value = (res.data || []).map(item => ({
        ...item,
        showEngineImages: false
      }));
      page.value.total = res.total;
    })
    .finally(() => {
      loading.close();
    });
  // if (type == "part") {
  //   api_post_spartPage({
  //     key: partKey.value,
  //     pageSize: 100,
  //   })
  //     .then((res) => {
  //       tableData.value = res.data || [];
  //     })
  //     .finally(() => {
  //       loading.close();
  //     });
  // } else {

  // }
};

// 新增的库存数量格式化方法
const formatStock = (value) => {
  if (!value) return '0'
  const number = Number(value)
  // 判断小数部分是否为零
  return number % 1 === 0
      ? number.toFixed(0)  // 整数部分
      : number.toFixed(2).replace(/\.?0+$/, '') // 保留非零小数并去除末尾零
}

const getImageUrl = (array, type) => {
  const data = array.reduce((total: string[], current: string) => {
    const urlArray = current.split("/").reverse();
    const isProduct = type !== "all" ? urlArray[0].includes("product") : true;
    if (
      urlArray.length > 0 &&
      isProduct &&
      (urlArray[0].includes(".jpg") || urlArray[0].includes(".png"))
    ) {
      total.push(base_downFileByPath_url + current);
    }
    return total;
  }, []);
  if (type === "all") {
    return data.reverse();
  }
  return data;
};

const getImageName = (url: string) => {
  const regex = /([^\/?#]+)(?=\.[a-zA-Z0-9]+[?#]|[?#]|$)/;
  const match = url.match(regex);
  return match?.[1]?.replace(/\.[^/.]+$/, "") || "";
};

const amountChange = async (item) => {
   item.totalPrice = parseFloat(
    (parseFloat(item.price) * parseFloat(item.amount)).toFixed(2)
  );
};

const addCart = (row, productType) => {
  row.buyLoad = true;
  api_post_addToCart([
    {
      amount: row.amount,
      bomItemsId: row.id,
      productType: productType,
    },
  ])
    .then((res) => {

      if ("0"==res.code) {
        row.existsCart="1";
      //  row.existsFavorites="1";

        ElMessage.success({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });

      }else if(("1"==res.code) ){
        row.existsCart="0";
     //   row.existsFavorites="0";
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else{
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });

      }


    })
    .finally(() => {
      row.buyLoad = false;
    });
};

const addFavorit = (row, productType) => {
  row.favoritLoad = true;
  api_post_addToFavorites([
    {
      bomItemsId: row.id,
      productType: productType,
    },
  ])
    .then((res) => {

      if ("0"==res.code) {
      //  row.existsCart="1";
        row.existsFavorites="1";
        ElMessage.success({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else if(("1"==res.code) ){
     //   row.existsCart="0";
        row.existsFavorites="0";
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });
      }else{
        ElMessage.error({
          message: res.msg,
          duration: 1000, // 显示时间，单位毫秒，这里设置为3秒
          offset: 150,
        });

      }


    })
    .finally(() => {
      row.favoritLoad = false;
    });
};

const jumpDetail = (item) => {
  console.log("/detail?params=" + item);
  router.push({
    path: "/detail",
    query: {
      params: item,
    },
  });
  console.log("---homeEquipment--------------------------");
  console.log(item);

  //用于面包屑跳转到 path: '/detail',
  //用于面包屑跳转到 path: '/detail',
  breadcrumbStore.clearBreadcrumbs()
  breadcrumbStore.addBreadcrumb({
    id: item,
    fileName: item.substring(item.lastIndexOf("/"), item.lastIndexOf(".")),
    filePath: item,
    type: "toDetail",
  });

};
const removeFileExtension = (filename)=> {
  const lastDotIndex = filename.lastIndexOf(".");
  if (lastDotIndex === -1) {
    // 没有找到点，表示没有扩展名
    return filename;
  }
  return filename.substring(0, lastDotIndex);
}
const currentChange = (current) => {
  page.value.current = current;
  getTableList();
};
onMounted(() => {
  getTableList("product");
});

const toggleEngineImages = (row) => {
  row.showEngineImages = !row.showEngineImages;
};

defineExpose({
  getTableList,
});
</script>

<style lang="less" scoped>
.home-table {
  /deep/.el-table__expand-column {
    > div {
      display: none;
    }
  }
  /deep/.el-table__header {
    background: #f5f5f5;
  }
  /deep/ .el-table__expanded-cell {
    padding: 0;
  }
  /deep/ th {
    padding: 0;
  }
}
.scrollbar-flex-content {
  display: flex;
}
.eq-image-warp {
  display: flex;
  flex-direction: column;
  margin-right: 16px;
  border: 2px solid #ececec;
  text-align: center;
  cursor: pointer;
  .eq-image-name {
    background-color: #ececec;
    padding: 4px 0 24px 12px;
    width: 100%;
    max-height: 50px;
    background-color: #ececec;
    height: 42px;
    padding: 8px;
    text-align: left;
  }
  .eq-image-item {
    padding: 6px;
    height: 100%;
  }
}
.more-button-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16px;
  min-width: 100px;
}

.engine-link {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--el-color-primary);
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    opacity: 0.8;
  }
  
  .arrow-icon {
    font-size: 14px;
    transform: rotate(0deg);
    transition: transform 0.3s;
    
    &.is-active {
      transform: rotate(90deg);
    }
  }
}

</style>

